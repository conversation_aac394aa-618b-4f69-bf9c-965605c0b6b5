import cron from "node-cron";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const resetDailyDataCron = () => {
  cron.schedule("0 20 * * *", async () => {
    try {
      await prisma.vendedor.updateMany({
        data: { leadsRecebidosHoje: 0, recebeuPrimeiroLead: false },
      });
      console.log("Dados diários resetados para todos os vendedores.");
    } catch (error) {
      console.error("Erro ao resetar dados diários:", error);
    }
  });
};
