/**
 * @swagger
 * /distribuir/distribuir-lead:
 *   post:
 *     summary: Distribui um lead para um vendedor adequado com base em regras específicas.
 *     tags:
 *       - Distribuir
 *     description: |
 *       Este endpoint recebe um lead do CRM e realiza a distribuição para um vendedor seguindo estas etapas principais:
 *
 *       **1. Verificar se o lead já está atribuído**
 *       - Caso o lead esteja atribuído a um vendedor:
 *         - Não Movimenta no crm.
 *         - Atualiza as métricas do vendedor na planilha do Google Sheets considerando os leads associados ao vendedor (`updateGoogleSheet`).
 *         - Envia uma notificação ao vendedor atual (`sentNotification`).
 *
 *       **2. Filtrar vendedores disponíveis**
 *       Os vendedores são selecionados com base em regras aplicadas por funções:
 *       - `filterVendedoresByInterval`: Filtra os vendedores considerando o intervalo mínimo entre leads.
 *       - `filterAndSortVendedores`: Ordena os vendedores com base em critérios como:
 *         - Leads de baixa pontuação (`isLowScoreLead`).
 *         - Leads indicados (`leadIndicado`).
 *         - Capacidade diária máxima de leads (`maxLeadsDia`).
 *
 *       **3. Selecionar o vendedor ideal (`selectVendor`)**
 *       - No início do dia:
 *         - Prioriza vendedores sem leads e com maior capacidade diária.
 *       - Após o início do dia:
 *         - Seleciona o vendedor com menor percentual de preenchimento do pote (`leadsRecebidosHoje / maxLeadsDia`).
 *         - Considera o tempo desde o último lead recebido em caso de empate.
 *       - Quando todos os vendedores atingiram o limite:
 *         - Prioriza vendedores que aceitam leads extras.
 *
 *       **4. Atualizar informações do lead e vendedor**
 *       - O lead é registrado ou atualizado no banco de dados (`createLead`).
 *       - O PipeRun é atualizado com o vendedor selecionado.
 *       - A planilha do Google Sheets é atualizada com as métricas do vendedor.
 *
 *       **5. Notificar o vendedor selecionado**
 *       - Envia uma mensagem com detalhes do lead ao vendedor (`sentNotification`).
 *
 *       ### Regras e Condições
 *       - **Intervalos de distribuição:**
 *         Configurados na planilha, determinam o tempo mínimo entre leads recebidos por um vendedor.
 *       - **Capacidade máxima diária:**
 *         Vendedores têm um limite configurado de leads por dia (`maxLeadsDia`).
 *       - **Aceitação de leads:**
 *         - Leads indicados têm prioridade para vendedores que aceitam indicações.
 *         - Leads de baixa pontuação são direcionados a vendedores configurados para recebê-los.
 *       - **Fallback:**
 *         Quando nenhum vendedor está disponível, o lead é marcado como fallback e registrado sem atribuição.
 *
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID do lead no CRM.
 *                 example: "12345"
 *               title:
 *                 type: string
 *                 description: Título do lead.
 *                 example: "Lead Importante"
 *               origin:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: Origem do lead.
 *                     example: "Indicação"
 *               user:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: Nome do cliente.
 *                     example: "João Silva"
 *               fields:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: Nome do campo do CRM.
 *                       example: "Lead Score"
 *                     value:
 *                       type: string
 *                       description: Valor associado ao campo.
 *                       example: "85"
 *     responses:
 *       200:
 *         description: Lead processado e distribuído com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem de sucesso.
 *                   example: "Lead registrado com sucesso e atribuído ao vendedor João Silva."
 *                 lead:
 *                   type: object
 *                   description: Detalhes do lead processado.
 *       404:
 *         description: Nenhum vendedor disponível. Lead marcado como fallback.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "Nenhum vendedor disponível. Lead marcado como fallback."
 *       500:
 *         description: Erro interno durante o processamento.
 */
