import cron from "node-cron";
import { redistributeFallbackLeads } from "../utils/distribution.utils";
import { logger } from "../utils/logger.utils";

export const redistributeFallbackLeadsCron = () => {
  cron.schedule("*/15 * * * *", async () => {
    try {
      logger.info("Iniciando redistribuição de leads em fallback...");
      await redistributeFallbackLeads();
      logger.info("Redistribuição de leads em fallback concluída.");
    } catch (error) {
      logger.error("Erro na redistribuição de leads em fallback", error);
    }
  });
};
