import axios from 'axios';
import { logger } from '../utils/logger.utils';

interface CustomField {
  id: number;
  value: string | string[];
  name: string;
}

interface CRMDeal {
  id: number;
  customFields: CustomField[];
}

export class CRMService {
  private static readonly BASE_URL = 'https://api.pipe.run/v1';
  private static readonly TOKEN = process.env.CRM_TOKEN;

  static async getDealById(crmId: string): Promise<CRMDeal | null> {
    try {
      const response = await axios.get(`${this.BASE_URL}/deals/${crmId}`, {
        params: { with: 'customFields' },
        headers: {
          accept: 'application/json',
          token: this.TOKEN
        }
      });

      if (response.data?.success) {
        return response.data.data;
      }

      return null;
    } catch (error) {
      logger.error(`Erro ao buscar deal ${crmId} no CRM:`, error);
      return null;
    }
  }

  static getCustomFieldValue(deal: CRMDeal, fieldId: number): string | string[] | null {
    const field = deal.customFields.find(f => f.id === fieldId);
    return field ? field.value : null;
  }

  // IDs dos campos customizados
  static readonly CUSTOM_FIELDS = {
    PORTE: 213683,
    ESTAGIO_NEGOCIO: 213595
  };
} 