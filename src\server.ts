import app from "./app";
import { prisma } from "./config";
import { redistributeFallbackLeads } from "./utils/distribution.utils";
import { logger } from "./utils/logger.utils";
import { setupReportCron } from "./cron/report.cron";

const PORT = 3039;

// Iniciar o servidor
app.listen(PORT, () => {
  logger.info(`🚀 Servidor rodando na porta http://localhost:${PORT}`);
  
  // Executar redistribuição de leads em fallback ao iniciar
  (async() => {
    await redistributeFallbackLeads();
  })();
  
  // Configurar cronjob para relatórios diários
  setupReportCron();
});