/**
 * @swagger
 * tags:
 *   - name: Sellers
 *     description: |
 *       Esta seção fornece uma referência estática com IDs, nomes e owner IDs dos vendedores cadastrados no sistema.
 *
 *       **Tabela de Referência**:
 *
 *       | ID SYS                                | ID CRM | Nome Completo               | Nome Abreviado  |
 *       |---------------------------------------|--------|-----------------------------|-----------------|
 *       | 2                                     | 78003  | <PERSON><PERSON><PERSON>            | <PERSON><PERSON>e S.    |
 *       | 4                                     | 79209  | <PERSON><PERSON>      | <PERSON><PERSON>.        |
 *       | 5                                     | 79212  | <PERSON><PERSON> Scaranto        | Delita S.       |
 *       | 6                                     | 79213  | P<PERSON><PERSON>la Fernanda Cortina   | Priscila C.     |
 *       | 9                                     | 83150  | <PERSON>                | <PERSON>.         |
 *       | 11                                    | 86127  | <PERSON>                      | <PERSON>       |
 *       | 12                                    | 86126  | <PERSON>run<PERSON>                       | <PERSON>run<PERSON>        |
 *       | 3cc41e34-68f6-49ac-bc3d-55c47b6349fb | 89707  | <PERSON>             | Amanda <PERSON>.       |
 *       | fefd9ac4-b262-4608-b017-9ae4a513609e | 89705  | Amanda Alves                | Amanda A.       |
 *       | ad03d0d8-9643-422d-ab58-2222a20931a6 | 89704  | Vitor Belucci               | Vitor B.        |
 *       | ed0764ec-3a99-4f25-ad36-79d716c2b30e | 89708  | Gustavo Testa               | Gustavo T.      |
 *       | ebfd89ba-2aa3-456f-bca4-398a92d13d1f | 91525  | Ariel                       | Ariel           |
 *       | 0df611c1-0b4d-4531-8beb-2f1b9a844952 | 91527  | Ana Flavia                  | Ana F.          |
 *       | 475773c6-5cea-4890-9d99-b51d1317ae23 | 91526  | Luis                        | Luis F.         |
 *
 *       **Observações**:
 *       - Todos os vendedores listados são da Registre.se
 *       - Os IDs SYS são UUIDs gerados pelo sistema
 *       - Os IDs CRM são os identificadores no PipeRun
 */

/**
 * Documentação dos Vendedores
 * 
 * Tabela de mapeamento dos vendedores no sistema:
 * 
 * | ID SYS                                | ID CRM | Nome Completo               | Nome Abreviado  |
 * |---------------------------------------|--------|-----------------------------|-----------------|
 * | 2                                     | 78003  | Jackeline Santos            | Jackeline S.    |
 * | 4                                     | 79209  | Leila de Oliveira Leme      | Leila L.        |
 * | 5                                     | 79212  | Delita Rosa Scaranto        | Delita S.       |
 * | 6                                     | 79213  | Priscila Fernanda Cortina   | Priscila C.     |
 * | 9                                     | 83150  | João Batista                | João B.         |
 * | 11                                    | 86127  | Amanda                      | Amanda G.       |
 * | 12                                    | 86126  | Bruna                       | Bruna S.        |
 * | 3cc41e34-68f6-49ac-bc3d-55c47b6349fb | 89707  | Amanda Oliveira             | Amanda O.       |
 * | fefd9ac4-b262-4608-b017-9ae4a513609e | 89705  | Amanda Alves                | Amanda A.       |
 * | ad03d0d8-9643-422d-ab58-2222a20931a6 | 89704  | Vitor Belucci               | Vitor B.        |
 * | ed0764ec-3a99-4f25-ad36-79d716c2b30e | 89708  | Gustavo Testa               | Gustavo T.      |
 * | ebfd89ba-2aa3-456f-bca4-398a92d13d1f | 91525  | Ariel                       | Ariel           |
 * | 0df611c1-0b4d-4531-8beb-2f1b9a844952 | 91527  | Ana Flavia                  | Ana F.          |
 * | 475773c6-5cea-4890-9d99-b51d1317ae23 | 91526  | Luis                        | Luis F.         |
 * 
 * Observações:
 * - Todos os vendedores listados são da Registre.se
 * - Os IDs SYS são UUIDs gerados pelo sistema
 * - Os IDs CRM são os identificadores no PipeRun
 */

/**
 * @swagger
 * /sellers/register-seller:
 *   post:
 *     summary: Cadastra um novo vendedor no sistema.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint permite registrar um novo vendedor no sistema. Ele recebe informações detalhadas do vendedor,
 *       incluindo nome completo, nome abreviado, horários de entrada e saída, quantidade máxima de leads por dia,
 *       entre outros dados. O vendedor é salvo no banco de dados e retorna os detalhes do registro.
 *
 *       **Campos importantes**:
 *       - `nome`: Nome completo do vendedor.
 *       - `nomeAbreviado`: Nome abreviado usado em sistemas de exibição ou relatórios.
 *       - `maxLeadsDia`: Número máximo de leads que o vendedor pode receber por dia.
 *       - `horarioEntrada` e `horarioSaida`: Horários de trabalho do vendedor.
 *       - `leadsExtras`: Permite definir se o vendedor pode receber leads extras.
 *       - `foraDoHorario`: Indica se o vendedor aceita leads fora do horário padrão.
 *       - `ownerId`: Identificador do vendedor no CRM associado a este registro.
 *
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nome:
 *                 type: string
 *                 description: Nome completo do vendedor.
 *                 example: João da Silva
 *               nomeAbreviado:
 *                 type: string
 *                 description: Nome abreviado do vendedor.
 *                 example: João S.
 *               maxLeadsDia:
 *                 type: integer
 *                 description: Número máximo de leads por dia.
 *                 example: 10
 *               horarioEntrada:
 *                 type: string
 *                 description: Horário de entrada no formato HH:mm.
 *                 example: "08:00"
 *               horarioSaida:
 *                 type: string
 *                 description: Horário de saída no formato HH:mm.
 *                 example: "17:00"
 *               leadsExtras:
 *                 type: boolean
 *                 description: Indica se o vendedor pode receber leads extras.
 *                 example: true
 *               foraDoHorario:
 *                 type: boolean
 *                 description: Indica se o vendedor aceita leads fora do horário de trabalho.
 *                 example: false
 *               ownerId:
 *                 type: string
 *                 description: ID do vendedor no CRM associado a este registro.
 *                 example: "crm-vendedor12345"
 *     responses:
 *       200:
 *         description: Vendedor cadastrado com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: ID do vendedor criado.
 *                   example: "vendedor12345"
 *                 nome:
 *                   type: string
 *                   description: Nome completo do vendedor.
 *                   example: João da Silva
 *                 nomeAbreviado:
 *                   type: string
 *                   description: Nome abreviado do vendedor.
 *                   example: João S.
 *                 maxLeadsDia:
 *                   type: integer
 *                   description: Número máximo de leads por dia.
 *                   example: 10
 *                 horarioEntrada:
 *                   type: string
 *                   description: Horário de entrada.
 *                   example: "08:00"
 *                 horarioSaida:
 *                   type: string
 *                   description: Horário de saída.
 *                   example: "17:00"
 *                 leadsExtras:
 *                   type: boolean
 *                   description: Permite leads extras.
 *                   example: true
 *                 foraDoHorario:
 *                   type: boolean
 *                   description: Permite leads fora do horário.
 *                   example: false
 *                 ownerId:
 *                   type: string
 *                   description: ID do vendedor no CRM associado a este registro.
 *                   example: "crm-vendedor12345"
 *       400:
 *         description: Requisição inválida. Verifique os dados enviados.
 *       500:
 *         description: Erro interno ao registrar o vendedor.
 */
/**
 * @swagger
 * /sellers/update-seller/{id}:
 *   put:
 *     summary: Atualiza as informações de um vendedor existente.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint permite atualizar as informações de um vendedor existente no sistema. É possível modificar dados como nome, horários de trabalho,
 *       quantidade máxima de leads, e outras configurações. O ID do vendedor deve ser informado na URL e as novas informações são enviadas no corpo da requisição.
 *
 *       **Campos importantes**:
 *       - `nome`: Nome completo do vendedor.
 *       - `nomeAbreviado`: Nome abreviado usado em relatórios ou exibição no sistema.
 *       - `maxLeadsDia`: Limite máximo de leads atribuídos ao vendedor por dia.
 *       - `ativo`: Define se o vendedor está ativo ou inativo no sistema.
 *       - `horarioEntrada` e `horarioSaida`: Intervalo de horário de trabalho.
 *       - `leadsExtras`: Define se o vendedor pode receber leads além do limite diário.
 *       - `foraDoHorario`: Permite ou não leads fora do horário padrão.
 *       - `ownerId`: ID do vendedor no CRM.
 *       - `appPass`: Senha do aplicativo associada ao vendedor.
 *
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do vendedor a ser atualizado.
 *         example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nome:
 *                 type: string
 *                 description: Nome completo do vendedor.
 *                 example: João da Silva
 *               nomeAbreviado:
 *                 type: string
 *                 description: Nome abreviado do vendedor.
 *                 example: João S.
 *               maxLeadsDia:
 *                 type: integer
 *                 description: Número máximo de leads atribuídos por dia.
 *                 example: 15
 *               ativo:
 *                 type: boolean
 *                 description: Define se o vendedor está ativo.
 *                 example: true
 *               horarioEntrada:
 *                 type: string
 *                 description: Horário de entrada no formato HH:mm.
 *                 example: "08:00"
 *               horarioSaida:
 *                 type: string
 *                 description: Horário de saída no formato HH:mm.
 *                 example: "18:00"
 *               leadsExtras:
 *                 type: boolean
 *                 description: Permite leads extras acima do limite.
 *                 example: false
 *               foraDoHorario:
 *                 type: boolean
 *                 description: Permite leads fora do horário de trabalho.
 *                 example: true
 *               ownerId:
 *                 type: string
 *                 description: ID do vendedor no CRM.
 *                 example: "crm-vendedor12345"
 *               appPass:
 *                 type: string
 *                 description: Senha associada ao vendedor no aplicativo.
 *                 example: "senha123"
 *     responses:
 *       200:
 *         description: Vendedor atualizado com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: ID do vendedor.
 *                   example: 1
 *                 nome:
 *                   type: string
 *                   description: Nome atualizado do vendedor.
 *                   example: João da Silva
 *                 nomeAbreviado:
 *                   type: string
 *                   description: Nome abreviado do vendedor.
 *                   example: João S.
 *                 maxLeadsDia:
 *                   type: integer
 *                   description: Novo limite de leads por dia.
 *                   example: 15
 *                 ativo:
 *                   type: boolean
 *                   description: Status ativo do vendedor.
 *                   example: true
 *                 horarioEntrada:
 *                   type: string
 *                   description: Horário de entrada atualizado.
 *                   example: "08:00"
 *                 horarioSaida:
 *                   type: string
 *                   description: Horário de saída atualizado.
 *                   example: "18:00"
 *                 leadsExtras:
 *                   type: boolean
 *                   description: Permite leads extras.
 *                   example: false
 *                 foraDoHorario:
 *                   type: boolean
 *                   description: Permite leads fora do horário.
 *                   example: true
 *                 ownerId:
 *                   type: string
 *                   description: ID atualizado no CRM.
 *                   example: "crm-vendedor12345"
 *                 appPass:
 *                   type: string
 *                   description: Senha associada ao aplicativo.
 *                   example: "senha123"
 *       400:
 *         description: Requisição inválida. Verifique os dados enviados.
 *       404:
 *         description: Vendedor não encontrado.
 *       500:
 *         description: Erro interno ao atualizar o vendedor.
 */
/**
 * @swagger
 * /sellers/update-seller-count/{id}:
 *   put:
 *     summary: Atualiza o contador de leads recebidos de um vendedor.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint permite atualizar a contagem de leads recebidos hoje para um vendedor específico no sistema.
 *       É necessário informar o ID do vendedor na URL e o novo valor de leads recebidos no corpo da requisição.
 *
 *       **Campo importante**:
 *       - `leadsRecebidosHoje`: Número atualizado de leads que o vendedor recebeu no dia atual.
 *
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do vendedor a ser atualizado.
 *         example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               leadsRecebidosHoje:
 *                 type: integer
 *                 description: Número de leads recebidos hoje.
 *                 example: 5
 *     responses:
 *       200:
 *         description: Contagem de leads atualizada com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: ID do vendedor.
 *                   example: 1
 *                 leadsRecebidosHoje:
 *                   type: integer
 *                   description: Número atualizado de leads recebidos hoje.
 *                   example: 5
 *       400:
 *         description: Requisição inválida. Verifique os dados enviados.
 *       404:
 *         description: Vendedor não encontrado.
 *       500:
 *         description: Erro interno ao atualizar a contagem de leads.
 */
/**
 * @swagger
 * /sellers/delete-seller/{id}:
 *   delete:
 *     summary: Remove um vendedor do sistema.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint permite deletar um vendedor do sistema com base no ID fornecido. 
 *       Antes de realizar a exclusão, o sistema verifica se o vendedor existe no banco de dados.
 *       Caso o vendedor seja encontrado, ele será removido permanentemente.
 *
 *       **Detalhes do fluxo:**
 *       1. Valida se o vendedor com o ID fornecido existe no banco de dados.
 *       2. Caso não exista, retorna um erro com código 404.
 *       3. Se encontrado, o vendedor é excluído e uma mensagem de sucesso é retornada.
 *
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do vendedor a ser deletado.
 *         example: 5
 *     responses:
 *       200:
 *         description: Vendedor deletado com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem de sucesso.
 *                   example: "Vendedor deletado com sucesso"
 *       404:
 *         description: Vendedor não encontrado.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro indicando que o vendedor não foi encontrado.
 *                   example: "Vendedor não encontrado"
 *       500:
 *         description: Erro interno ao deletar o vendedor.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro interno.
 *                   example: "Erro ao deletar vendedor"
 */
/**
 * @swagger
 * /sellers/get-sellers:
 *   get:
 *     summary: Obtém a lista de vendedores e seus leads dentro de um intervalo de tempo específico.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint retorna uma lista de vendedores com seus respectivos leads criados dentro do intervalo
 *       definido entre as 20h do dia anterior e as 20h do dia atual. Os horários são ajustados para o fuso horário
 *       "America/Sao_Paulo", e os dados são retornados em ordem crescente pelo ID do vendedor.
 *
 *       **Intervalo de Tempo:**
 *       - Início: 20h do dia anterior.
 *       - Fim: 20h do dia atual.
 *
 *       O sistema realiza as seguintes etapas:
 *       1. Converte os horários de início e fim para UTC, já que o banco de dados armazena os valores neste formato.
 *       2. Busca os vendedores e seus leads criados dentro do intervalo.
 *       3. Converte o horário de criação dos leads (`createdAt`) para o fuso horário "America/Sao_Paulo".
 *       4. Retorna os vendedores com seus leads serializados.
 *
 *     responses:
 *       200:
 *         description: Lista de vendedores e seus leads retornada com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     description: ID do vendedor.
 *                     example: 1
 *                   nome:
 *                     type: string
 *                     description: Nome completo do vendedor.
 *                     example: João da Silva
 *                   nomeAbreviado:
 *                     type: string
 *                     description: Nome abreviado do vendedor.
 *                     example: João S.
 *                   maxLeadsDia:
 *                     type: integer
 *                     description: Máximo de leads por dia.
 *                     example: 10
 *                   ativo:
 *                     type: boolean
 *                     description: Indica se o vendedor está ativo.
 *                     example: true
 *                   horarioEntrada:
 *                     type: string
 *                     description: Horário de entrada do vendedor.
 *                     example: "08:00"
 *                   horarioSaida:
 *                     type: string
 *                     description: Horário de saída do vendedor.
 *                     example: "17:00"
 *                   leads:
 *                     type: array
 *                     description: Lista de leads atribuídos ao vendedor dentro do intervalo de tempo.
 *                     items:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           description: ID do lead.
 *                           example: "16afe373-25ab-45c3-81ce-b34070e5fbff"
 *                         nome:
 *                           type: string
 *                           description: Nome do lead.
 *                           example: Empresa XYZ
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                           description: Data e hora de criação do lead no formato ajustado para "America/Sao_Paulo".
 *                           example: "28/12/2024, 21:11:28"
 *       500:
 *         description: Erro interno ao buscar os vendedores.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "Erro ao buscar vendedores"
 */
/**
 * @swagger
 * /sellers/get-todays-lead/{id}:
 *   get:
 *     summary: Obtém os leads atribuídos a um vendedor no dia atual.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint retorna a lista de leads atribuídos a um vendedor específico no dia atual.
 *       O intervalo considerado é das 00:00 até as 20:00 do dia atual, ajustado para o fuso horário "America/Sao_Paulo".
 *
 *       **Processo**:
 *       - Recebe o `id` do vendedor como parâmetro de rota.
 *       - Valida se o `id` é um número válido.
 *       - Define o início (00:00) e o fim (20:00) do intervalo no fuso horário "America/Sao_Paulo".
 *       - Converte os horários para UTC, pois o banco de dados utiliza esse formato.
 *       - Busca os leads criados dentro do intervalo para o vendedor especificado.
 *
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do vendedor.
 *         example: 5
 *     responses:
 *       200:
 *         description: Lista de leads atribuídos ao vendedor no dia atual.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: ID do lead.
 *                     example: "16afe373-25ab-45c3-81ce-b34070e5fbff"
 *                   nome:
 *                     type: string
 *                     description: Nome do lead.
 *                     example: Empresa XYZ
 *                   vendedorId:
 *                     type: integer
 *                     description: ID do vendedor associado ao lead.
 *                     example: 5
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     description: Data e hora de criação do lead.
 *                     example: "2024-12-28T12:34:56Z"
 *       400:
 *         description: ID de vendedor inválido.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "ID de vendedor inválido"
 *       500:
 *         description: Erro interno ao buscar os leads do dia.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "Erro ao buscar leads do dia"
 */
/**
 * @swagger
 * /sellers/get-seller-leads/{id}:
 *   get:
 *     summary: Obtém todos os leads atribuídos a um vendedor específico.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint retorna a lista de todos os leads atribuídos a um vendedor específico.
 *       A consulta é feita com base no `id` do vendedor fornecido como parâmetro de rota.
 *
 *       **Processo**:
 *       - Recebe o `id` do vendedor como parâmetro de rota.
 *       - Valida se o `id` é um número válido.
 *       - Busca os leads associados ao vendedor, incluindo informações do vendedor e do lead.
 *
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do vendedor.
 *         example: 5
 *     responses:
 *       200:
 *         description: Lista de leads atribuídos ao vendedor.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: ID do lead.
 *                     example: "16afe373-25ab-45c3-81ce-b34070e5fbff"
 *                   nome:
 *                     type: string
 *                     description: Nome do lead.
 *                     example: Empresa XYZ
 *                   crmId:
 *                     type: string
 *                     description: ID do lead no CRM.
 *                     example: "43803154"
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     description: Data e hora de criação do lead.
 *                     example: "2024-12-28T12:34:56Z"
 *                   vendedor:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: ID do vendedor associado ao lead.
 *                         example: 5
 *                       nome:
 *                         type: string
 *                         description: Nome do vendedor associado ao lead.
 *                         example: João da Silva
 *       400:
 *         description: ID de vendedor inválido.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "ID de vendedor inválido"
 *       500:
 *         description: Erro interno ao buscar os leads do vendedor.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "Erro ao buscar leads do vendedor"
 */
/**
 * @swagger
 * /sellers/get-all-leads:
 *   get:
 *     summary: Obtém todos os leads registrados no sistema.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint retorna a lista de todos os leads cadastrados no sistema, incluindo informações
 *       como nome do lead, ID no CRM, data de criação e o nome do vendedor associado (se houver).
 *
 *       **Processo**:
 *       - Consulta todos os leads do banco de dados.
 *       - Retorna informações básicas de cada lead, incluindo o vendedor associado.
 *
 *     responses:
 *       200:
 *         description: Lista de todos os leads registrados no sistema.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   vendedor:
 *                     type: object
 *                     properties:
 *                       nome:
 *                         type: string
 *                         description: Nome do vendedor associado ao lead.
 *                         example: João da Silva
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     description: Data e hora de criação do lead.
 *                     example: "2024-12-28T12:34:56Z"
 *                   crmId:
 *                     type: string
 *                     description: ID do lead no CRM.
 *                     example: "43803154"
 *                   nome:
 *                     type: string
 *                     description: Nome do lead.
 *                     example: Empresa XYZ
 *       500:
 *         description: Erro interno ao buscar todos os leads.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Mensagem de erro.
 *                   example: "Erro ao buscar todos os leads"
 */
/**
 * @swagger
 * /sellers/desatribuir-lead:
 *   post:
 *     summary: Desatribui um lead de um vendedor e o remove do banco de dados.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint permite desatribuir um lead de um vendedor específico no sistema. 
 *       Quando um lead é desatribuído, ele é removido do banco de dados, e o contador de leads 
 *       recebidos pelo vendedor no dia é decrementado automaticamente.
 *
 *       **Processamento:**
 *       1. O sistema verifica se o lead existe no banco de dados pelo `crmId` fornecido.
 *       2. Caso o lead exista, ele é removido.
 *       3. Se o lead estava associado a um vendedor, o contador `leadsRecebidosHoje` do vendedor é decrementado.
 *
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: CRM ID do lead a ser desatribuído.
 *                 example: "43803154"
 *     responses:
 *       200:
 *         description: Lead desatribuído e removido com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 lead:
 *                   type: object
 *                   description: Dados do lead desatribuído.
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: ID interno do lead.
 *                       example: "16afe373-25ab-45c3-81ce-b34070e5fbff"
 *                     vendedor:
 *                       type: object
 *                       description: Dados do vendedor associado ao lead (se aplicável).
 *                       properties:
 *                         id:
 *                           type: integer
 *                           description: ID do vendedor.
 *                           example: 5
 *                         nome:
 *                           type: string
 *                           description: Nome do vendedor.
 *                           example: "Delita Rosa Scaranto"
 *       404:
 *         description: Lead não encontrado.
 *       500:
 *         description: Erro ao desatribuir o lead.
 */

/**
 * @swagger
 * /sellers/reset-count:
 *   post:
 *     summary: Reseta a contagem diária de leads recebidos para todos os vendedores.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint redefine o campo `leadsRecebidosHoje` de todos os vendedores para zero.
 *       Ele é usado para reiniciar a contagem diária de leads atribuídos a cada vendedor.
 *
 *     responses:
 *       200:
 *         description: Contagem de leads resetada com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem de sucesso da operação.
 *                   example: "Contagem de leads resetada com sucesso"
 *       500:
 *         description: Erro ao resetar a contagem de leads.
 */
/**
 * @swagger
 * /sellers/reatribuir-lead:
 *   post:
 *     summary: Redistribui um lead recebido via webhook do CRM.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint é acionado por um webhook do CRM quando é necessário redistribuir um lead. Ele realiza as seguintes ações:
 *       1. **Busca o lead no banco de dados** para validar sua existência e verificar o vendedor atribuído.
 *       2. **Desatribui o lead do vendedor atual**, caso esteja atribuído, e decrementa a contagem de leads do vendedor.
 *       3. **Remove o lead do banco de dados** para prepará-lo para redistribuição.
 *       4. **Redistribui o lead** utilizando a lógica de distribuição com base no estágio definido.
 *       
 *       **Detalhes importantes:**
 *       - O `leadId` é recebido no corpo da requisição e corresponde ao identificador do lead no CRM.
 *       - O campo `stageIdToMove` indica o ID do estágio no CRM onde o lead será movido para redistribuição(432437).
 *       - O campo `spreadsheetId` representa o ID de uma planilha vinculada à lógica de distribuição, se aplicável.
 *       - Caso o lead esteja atribuído a um vendedor, a contagem de leads atribuídos ao vendedor será decrementada.
 *       - A lógica de redistribuição é realizada através da função `distributeLead`, utilizando os dados originais do lead.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID do lead no CRM.
 *                 example: "123456"
 *     responses:
 *       200:
 *         description: Lead redistribuído com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indica se a redistribuição foi bem-sucedida.
 *                   example: true
 *                 message:
 *                   type: string
 *                   description: Mensagem com detalhes da redistribuição.
 *                   example: "Lead redistribuído com sucesso."
 *                 details:
 *                   type: object
 *                   description: Detalhes do resultado da redistribuição.
 *       404:
 *         description: Lead não encontrado no banco de dados.
 *       500:
 *         description: Erro interno ao processar a redistribuição do lead.
 */
/**
 * @swagger
 * /sellers/update-sellers-by-sheets:
 *   get:
 *     summary: Atualiza os dados dos vendedores com base nas informações da planilha base.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint é responsável por sincronizar os dados dos vendedores a partir de uma planilha configurada no sistema.
 *       A planilha serve como fonte de verdade para informações atualizadas dos vendedores, como horários de trabalho,
 *       quantidade máxima de leads por dia, status ativo ou inativo, entre outros detalhes.
 *
 *       **Processo:**
 *       1. O sistema conecta-se à API do Google Sheets ou outra fonte configurada para buscar os dados.
 *       2. As informações são processadas e comparadas com os dados existentes no banco de dados.
 *       3. Atualizações são aplicadas, incluindo a criação, edição ou desativação de registros de vendedores conforme necessário.
 *
 *     responses:
 *       200:
 *         description: Dados dos vendedores atualizados com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem de sucesso.
 *                   example: "Vendedores atualizados com sucesso."
 *                 updated:
 *                   type: array
 *                   description: Lista de vendedores atualizados.
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: ID do vendedor.
 *                         example: 1
 *                       nome:
 *                         type: string
 *                         description: Nome do vendedor.
 *                         example: "João da Silva"
 *                       status:
 *                         type: string
 *                         description: Status da operação (atualizado/criado/desativado).
 *                         example: "atualizado"
 *       500:
 *         description: Erro ao atualizar os vendedores a partir da planilha.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Detalhes do erro ocorrido.
 *                   example: "Erro ao conectar à API do Google Sheets."
 */
/**
 * @swagger
 * /sellers/{id}/update-token:
 *   post:
 *     summary: Atualiza o token de notificação do vendedor.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint atualiza o token de notificação associado a um vendedor específico.
 *       O token é utilizado para envio de notificações push via aplicativo.
 *
 *       **Processo:**
 *       1. Recebe o ID do vendedor como parâmetro de rota.
 *       2. Atualiza o campo `appToken` no banco de dados com o token recebido no corpo da requisição.
 *
 *       **Possíveis erros:**
 *       - Caso o vendedor não exista, um erro será retornado.
 *       - Em caso de falha na atualização do banco, será retornado um erro interno do servidor.
 *
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do vendedor a ser atualizado.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *                 description: Token do aplicativo para notificações.
 *                 example: ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]
 *     responses:
 *       200:
 *         description: Token atualizado com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem de sucesso.
 *                   example: Token atualizado com sucesso
 *       500:
 *         description: Erro ao atualizar token.
 */

/**
 * @swagger
 * /sellers/app-login:
 *   post:
 *     summary: Realiza o login do vendedor no aplicativo.
 *     tags:
 *       - Sellers
 *     description: |
 *       Este endpoint permite que um vendedor realize login no aplicativo com base no `ownerId` e senha.
 *       Retorna informações como leads atribuídos ao vendedor, leads recebidos no dia, e nome abreviado do vendedor.
 *
 *       **Processo:**
 *       1. Recebe o `ownerId` e a senha do vendedor no corpo da requisição.
 *       2. Valida se o `ownerId` é válido e se a senha corresponde ao registro no banco de dados.
 *       3. Retorna informações do vendedor, incluindo leads atribuídos e nome abreviado.
 *
 *       **Possíveis erros:**
 *       - Caso o `ownerId` não corresponda a um vendedor, será retornado um erro 404.
 *       - Caso a senha seja inválida, será retornado um erro 401.
 *       - Em caso de falha na consulta ao banco de dados, será retornado um erro interno do servidor.
 *
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ownerId:
 *                 type: integer
 *                 description: Identificador do vendedor no CRM.
 *                 example: 71078
 *               password:
 *                 type: string
 *                 description: Senha do vendedor para login.
 *                 example: "123456"
 *     responses:
 *       200:
 *         description: Login realizado com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: ID do vendedor.
 *                   example: 1
 *                 success:
 *                   type: boolean
 *                   description: Indica se o login foi bem-sucedido.
 *                   example: true
 *                 leads:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: ID do lead.
 *                         example: "lead12345"
 *                       nome:
 *                         type: string
 *                         description: Nome do lead.
 *                         example: "Empresa XYZ"
 *                 leadsRecebidosHoje:
 *                   type: integer
 *                   description: Número de leads recebidos pelo vendedor hoje.
 *                   example: 5
 *                 nomeAbreviado:
 *                   type: string
 *                   description: Nome abreviado do vendedor.
 *                   example: "João S."
 *       404:
 *         description: Vendedor não encontrado.
 *       401:
 *         description: Senha inválida.
 *       500:
 *         description: Erro interno ao realizar login.
 */
