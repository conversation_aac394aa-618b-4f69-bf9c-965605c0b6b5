{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "ts-node src/server.ts", "report": "ts-node src/cli/generate-report.ts", "build-report": "tsc src/cli/generate-report.ts --outDir dist"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.10.2", "@types/node-cron": "^3.0.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/ws": "^8.5.13", "prisma": "^6.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.7.2"}, "dependencies": {"@prisma/client": "^6.1.0", "@types/moment": "^2.11.29", "axios": "^1.8.4", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "googleapis": "^144.0.0", "moment": "^2.30.1", "node-cron": "^3.0.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ws": "^8.18.0"}}