import axios from "axios";
import * as config from "../config";
import WebSocket from "ws";
import { Lead, PrismaClient } from "@prisma/client";
import { createLead } from "../services/distribution.service";
import dotenv from "dotenv";
import { logger } from "./logger.utils";
dotenv.config();
const WebSocketClient = new WebSocket(
  "wss://wss.registreme.com.br:8383?id=Api-LeadHubBackend"
);
const prisma = new PrismaClient();
const spreadsheetId = "1HRn5UJJnLuiZb6pb9fNwvDAGDR6O3FCZkz5fBz7pE-s";
WebSocketClient.on("open", function open() {
  const mensagemInicial = JSON.stringify({
    tipo: "saudacao",
    mensagem: `Olá, sou  API-LeadHubBackend e me conectei ao servidor.`,
  });
  WebSocketClient.send(mensagemInicial);
});
// Definição da interface Vendedor
interface Vendedor {
  id: string;
  ativo: boolean;
  ownerId: number;
  nome: string;
  nomeAbreviado: string;
  maxLeadsDia: number;
  leadsRecebidosHoje: number;
  leadsIndicados: boolean;
  leadsLowScore: boolean;
  leadsExtras: boolean;
  foraDoHorario: boolean;
  horarioEntrada?: string;
  horarioSaida?: string;
  leads: Lead[];
  ultimoLeadDate?: Date; // Adicionado para os vendedores com dados de último lead
}
type PrismaVendedor = {
  id: number;
  ativo: boolean;
  ownerId: number | null;
  nome: string | null;
  nomeAbreviado: string | null;
  maxLeadsDia: number | null;
  leadsRecebidosHoje: number | null;
  leadsIndicados: boolean;
  leadsLowScore: boolean;
  leadsExtras: boolean;
  foraDoHorario: boolean;
  horarioEntrada?: string;
  horarioSaida?: string;
  leads: Lead[]; // Assumindo que Lead é outro tipo definido
};

export const updateLeadInPipeRun = async (
  leadId: string,
  stageIdToMove: number,
  selectedVendorOwnerId: number,
  haveOwner = false
) => {
  const apiUrl = `https://api.pipe.run/v1/deals/${leadId}`;
  let requestData = {};
  
  if (haveOwner) {
    requestData = {
      owner_id: selectedVendorOwnerId,
    };
  } else {
    requestData = {
      stage_id: stageIdToMove,
      owner_id: selectedVendorOwnerId,
    };
  }

  const headers = {
    accept: "application/json",
    "content-type": "application/json",
    token: process.env.CRM_TOKEN,
  };

  try {
    await axios.put(apiUrl, requestData, { headers });
    logger.info(`Lead ${leadId} atualizado no PipeRun com sucesso!`);
  } catch (error) {
    logger.error(`Erro ao atualizar lead ${leadId} no PipeRun`, error);
    throw new Error("Erro ao atualizar lead no PipeRun");
  }
};
export const filterAndSortVendedores = async (vendedores: Vendedor[]): Promise<Vendedor[]> => {
  const currentTime = new Date();
  const currentHour = currentTime.getHours();
  const currentMinutes = currentTime.getMinutes();

  logger.info(`Hora atual: ${currentHour}:${currentMinutes}`);

  // Log inicial com valores de cada vendedor antes de qualquer filtro
  logger.info("Dados dos vendedores antes da filtragem", 
    vendedores.map(v => ({
      id: v.id,
      nome: v.nome,
      leadsRecebidosHoje: v.leadsRecebidosHoje,
      maxLeadsDia: v.maxLeadsDia,
      ativo: v.ativo
    }))
  );

  // Verificar e atualizar valores inválidos consultando o banco de dados
  const vendedoresAtualizados = await Promise.all(
    vendedores.map(async (vendedor) => {
      // Se os valores estiverem indefinidos, nulos ou inválidos, buscar do banco
      if (vendedor.leadsRecebidosHoje === null || 
          vendedor.leadsRecebidosHoje === undefined || 
          vendedor.maxLeadsDia === null || 
          vendedor.maxLeadsDia === undefined || 
          vendedor.maxLeadsDia <= 0) {
        
        logger.warning(`Vendedor ${vendedor.nome} com dados incompletos. Consultando banco de dados...`);
        
        try {
          const vendedorAtualizado = await prisma.vendedor.findUnique({
            where: { id: vendedor.id },
            select: { leadsRecebidosHoje: true, maxLeadsDia: true }
          });
          
          if (vendedorAtualizado) {
            if (vendedor.leadsRecebidosHoje === null || vendedor.leadsRecebidosHoje === undefined) {
              vendedor.leadsRecebidosHoje = vendedorAtualizado.leadsRecebidosHoje || 0;
              logger.info(`Atualizando leadsRecebidosHoje de ${vendedor.nome} para ${vendedor.leadsRecebidosHoje}`);
            }
            
            if (vendedor.maxLeadsDia === null || vendedor.maxLeadsDia === undefined || vendedor.maxLeadsDia <= 0) {
              vendedor.maxLeadsDia = vendedorAtualizado.maxLeadsDia || 10;
              logger.info(`Atualizando maxLeadsDia de ${vendedor.nome} para ${vendedor.maxLeadsDia}`);
            }
          } else {
            // Se não encontrou no banco, usar valores padrão
            logger.warning(`Vendedor ${vendedor.nome} não encontrado no banco. Usando valores padrão.`);
            if (vendedor.leadsRecebidosHoje === null || vendedor.leadsRecebidosHoje === undefined) {
              vendedor.leadsRecebidosHoje = 0;
            }
            
            if (vendedor.maxLeadsDia === null || vendedor.maxLeadsDia === undefined || vendedor.maxLeadsDia <= 0) {
              vendedor.maxLeadsDia = 10;
            }
          }
        } catch (error) {
          logger.error(`Erro ao buscar dados atualizados para ${vendedor.nome}`, error);
          // Em caso de erro, usar valores padrão
          if (vendedor.leadsRecebidosHoje === null || vendedor.leadsRecebidosHoje === undefined) {
            vendedor.leadsRecebidosHoje = 0;
          }
          
          if (vendedor.maxLeadsDia === null || vendedor.maxLeadsDia === undefined || vendedor.maxLeadsDia <= 0) {
            vendedor.maxLeadsDia = 10;
          }
        }
      }
      
      return vendedor;
    })
  );

  // Função auxiliar para verificar se o vendedor está dentro do horário de trabalho
  const isWithinWorkingHours = (vendedor: Vendedor): boolean => {
    const { horarioEntrada, horarioSaida } = vendedor;

    // Verificar se horarioEntrada ou horarioSaida é null ou undefined
    if (!horarioEntrada || !horarioSaida) {
      logger.warning(`Vendedor ${vendedor.nome} não tem horário definido.`);
      return false;
    }

    // Extrair horas e minutos do horário de entrada do vendedor
    const [entradaHour, entradaMinutes] = horarioEntrada.split(":").map(Number);

    // Extrair horas e minutos do horário de saída do vendedor
    const [saidaHour, saidaMinutes] = horarioSaida.split(":").map(Number);

    // Verificar se o horário atual está entre o horário de entrada e saída do vendedor
    const dentroHorario =
      (currentHour > entradaHour ||
        (currentHour === entradaHour && currentMinutes >= entradaMinutes)) &&
      (currentHour < saidaHour ||
        (currentHour === saidaHour && currentMinutes <= saidaMinutes));

    logger.timeCheck(vendedor, dentroHorario);

    return dentroHorario;
  };

  // Filtrar vendedores dentro do horário de trabalho
  let availableVendedores = vendedoresAtualizados.filter(isWithinWorkingHours);

  logger.info(
    `Vendedores dentro do horário: ${availableVendedores.length}`,
    availableVendedores.map((v) => v.nome)
  );
  // Se não houver vendedores dentro do horário de trabalho, incluir vendedores fora do horário

  if (availableVendedores.length === 0) {
    availableVendedores = vendedoresAtualizados.filter(
      (vendedor) => vendedor.foraDoHorario
    );
    logger.info(
      `Vendedores fora do horário permitidos: ${availableVendedores.length}`,
      availableVendedores.map((v) => v.nome)
    );
  }

  // Ordenar os vendedores pela proporção de leads recebidos hoje em relação ao máximo de leads diários permitidos
  availableVendedores.sort((a, b) => {
    // Validar valores para evitar divisões por zero ou valores inválidos
    const aLeads = typeof a.leadsRecebidosHoje === 'number' ? a.leadsRecebidosHoje : 0;
    const bLeads = typeof b.leadsRecebidosHoje === 'number' ? b.leadsRecebidosHoje : 0;
    const aMax = (typeof a.maxLeadsDia === 'number' && a.maxLeadsDia > 0) ? a.maxLeadsDia : 10; // Valor padrão se inválido
    const bMax = (typeof b.maxLeadsDia === 'number' && b.maxLeadsDia > 0) ? b.maxLeadsDia : 10; // Valor padrão se inválido
    
    const percentageA = aLeads / aMax;
    const percentageB = bLeads / bMax;
    return percentageA - percentageB;
  });

  logger.info(
    `Vendedores após ordenação por ocupação: ${availableVendedores.length}`,
    availableVendedores.map((v) => ({
      nome: v.nome,
      ocupacao: `${v.leadsRecebidosHoje}/${v.maxLeadsDia}`,
      porcentagem: `${Math.round((v.leadsRecebidosHoje || 0) / (v.maxLeadsDia || 1) * 100)}%`
    }))
  );

  return availableVendedores;
};
export const selectVendor = async (
  vendedores: any[],
  leadIndicado: boolean,
  isLowScoreLead: boolean
): Promise<{
  selectedVendorOwnerId: number | null;
  selectedVendorId: string | null;
  vendedorNome: string | null;
  nomeAbreviado: string | null;
}> => {
  logger.info("Iniciando seleção de vendedor...");
  try {
    // Verificar e corrigir valores inválidos antes de iniciar a seleção
    // usando consultas ao banco de dados para obter dados atualizados
    const vendedoresAtualizados = await Promise.all(
      vendedores.map(async (vendedor) => {
        // Sempre consultar o banco para ter os valores mais atualizados
        try {
          const vendedorAtualizado = await config.prisma.vendedor.findUnique({
            where: { id: vendedor.id },
            select: { leadsRecebidosHoje: true, maxLeadsDia: true }
          });
          
          if (vendedorAtualizado) {
            // Verificar se os valores são diferentes antes de atualizar
            if (vendedor.leadsRecebidosHoje !== vendedorAtualizado.leadsRecebidosHoje) {
              logger.info(`Atualizando leadsRecebidosHoje de ${vendedor.nome} de ${vendedor.leadsRecebidosHoje} para ${vendedorAtualizado.leadsRecebidosHoje}`);
              vendedor.leadsRecebidosHoje = vendedorAtualizado.leadsRecebidosHoje || 0;
            }
            
            if (vendedor.maxLeadsDia !== vendedorAtualizado.maxLeadsDia) {
              logger.info(`Atualizando maxLeadsDia de ${vendedor.nome} de ${vendedor.maxLeadsDia} para ${vendedorAtualizado.maxLeadsDia}`);
              vendedor.maxLeadsDia = vendedorAtualizado.maxLeadsDia || 10;
            }
          } else {
            // Se não encontrou no banco, usar valores padrão
            logger.warning(`Vendedor ${vendedor.nome} não encontrado no banco durante a seleção. Usando valores padrão.`);
            if (vendedor.leadsRecebidosHoje === null || vendedor.leadsRecebidosHoje === undefined) {
              vendedor.leadsRecebidosHoje = 0;
            }
            
            if (vendedor.maxLeadsDia === null || vendedor.maxLeadsDia === undefined || vendedor.maxLeadsDia <= 0) {
              vendedor.maxLeadsDia = 10;
            }
          }
        } catch (error) {
          logger.error(`Erro ao buscar dados atualizados para ${vendedor.nome} durante a seleção`, error);
          // Em caso de erro, garantir que os valores não são null ou undefined
          if (vendedor.leadsRecebidosHoje === null || vendedor.leadsRecebidosHoje === undefined) {
            vendedor.leadsRecebidosHoje = 0;
          }
          
          if (vendedor.maxLeadsDia === null || vendedor.maxLeadsDia === undefined || vendedor.maxLeadsDia <= 0) {
            vendedor.maxLeadsDia = 10;
          }
        }
        
        return vendedor;
      })
    );

    // Log mostrando os valores atualizados de cada vendedor
    logger.info("Valores de vendedores após atualização do banco:", 
      vendedoresAtualizados.map(vendedor => ({
        nome: vendedor.nome,
        leads_hoje: vendedor.leadsRecebidosHoje,
        pote: vendedor.maxLeadsDia,
        porcentagem: `${Math.round((vendedor.leadsRecebidosHoje / vendedor.maxLeadsDia) * 100)}%`
      }))
    );

    return await config.prisma.$transaction(async (prismaTransaction) => {
      // 1. Filtrar vendedores ativos
      let vendedoresAtivos = vendedoresAtualizados.filter((vendedor) => vendedor.ativo);
      logger.selectionCriteria("Filtro de vendedores ativos", {
        total: vendedores.length,
        ativos: vendedoresAtivos.length,
      });

      // 2. Filtrar vendedores que aceitam leads indicados e/ou leads de baixa pontuação
      let vendedoresDisponiveis = vendedoresAtivos.filter((vendedor) => {
        return (
          (leadIndicado && vendedor.leadsIndicados) ||
          (isLowScoreLead && vendedor.leadsLowScore)
        );
      });

      logger.selectionCriteria(
        `Filtro por tipo de lead (Indicado: ${leadIndicado}, LowScore: ${isLowScoreLead})`,
        {
          antes: vendedoresAtivos.length,
          depois: vendedoresDisponiveis.length,
          vendedores: vendedoresDisponiveis.map((v) => v.nome),
        }
      );

      // Não há mais exclusão explícita dos prioritários em leads normais!

      if (vendedoresDisponiveis.length === 0) {
        logger.warning(
          "Nenhum vendedor disponível para o tipo de lead, utilizando todos os vendedores ativos"
        );
        vendedoresDisponiveis = vendedoresAtivos;
      }

      logger.availableVendors(vendedoresDisponiveis);

      const inicioDoDia = vendedoresDisponiveis.every(
        (vendedor) => vendedor.leadsRecebidosHoje === 0
      );
      logger.selectionCriteria("É início do dia?", { inicioDoDia });

      let selectedVendor: Vendedor;

      if (inicioDoDia) {
        const vendedoresSemLeadsHoje = vendedoresDisponiveis.filter(
          (vendedor) => vendedor.leadsRecebidosHoje === 0
        );

        logger.selectionCriteria("Vendedores sem leads hoje", {
          total: vendedoresSemLeadsHoje.length,
          vendedores: vendedoresSemLeadsHoje.map((v) => v.nome),
        });

        const maiorTamanhoPote = Math.max(
          ...vendedoresSemLeadsHoje.map((v) => v.maxLeadsDia)
        );
        logger.selectionCriteria("Maior tamanho de pote", { maiorTamanhoPote });

        const vendedoresMaiorPoteSemLeads = vendedoresSemLeadsHoje.filter(
          (vendedor) => vendedor.maxLeadsDia === maiorTamanhoPote
        );

        logger.selectionCriteria("Vendedores com maior pote sem leads", {
          total: vendedoresMaiorPoteSemLeads.length,
          vendedores: vendedoresMaiorPoteSemLeads.map((v) => v.nome),
        });

        const randomIndex = Math.floor(
          Math.random() * vendedoresMaiorPoteSemLeads.length
        );
        selectedVendor = vendedoresMaiorPoteSemLeads[randomIndex];

        logger.selectionCriteria(
          "Vendedor selecionado aleatoriamente (início do dia)",
          {
            nome: selectedVendor.nome,
            index: randomIndex,
            totalOpcoes: vendedoresMaiorPoteSemLeads.length,
          }
        );

        // Verificar se o vendedor existe no banco antes de atualizar
        const vendedorExistente = await prismaTransaction.vendedor.findUnique({
          where: { id: selectedVendor.id }
        });
        
        if (!vendedorExistente) {
          logger.error(`Vendedor com ID ${selectedVendor.id} não encontrado no banco`, {
            errorType: "VendedorNotFound"
          });
          throw new Error(`Vendedor com ID ${selectedVendor.id} não encontrado no banco`);
        }
      } else {
        const allPotesCheios = vendedoresDisponiveis.every(
          (vendedor) => {
            // Validar valores antes de comparar
            const leadsHoje = typeof vendedor.leadsRecebidosHoje === 'number' ? vendedor.leadsRecebidosHoje : 0;
            const maxLeads = (typeof vendedor.maxLeadsDia === 'number' && vendedor.maxLeadsDia > 0) ? 
                            vendedor.maxLeadsDia : 10; // Valor padrão se inválido
            return leadsHoje >= maxLeads;
          }
        );
        logger.selectionCriteria("Todos os potes estão cheios?", {
          allPotesCheios,
          detalhes: vendedoresDisponiveis.map((v) => {
            // Validar valores para cada vendedor
            const leadsHoje = typeof v.leadsRecebidosHoje === 'number' ? v.leadsRecebidosHoje : 0;
            const maxLeads = (typeof v.maxLeadsDia === 'number' && v.maxLeadsDia > 0) ? 
                           v.maxLeadsDia : 10; // Valor padrão se inválido
            const porcentagem = Math.round((leadsHoje / maxLeads) * 100);
            
            return {
              nome: v.nome,
              leadsRecebidosHoje: leadsHoje,
              maxLeadsDia: maxLeads,
              porcentagem: `${porcentagem}%`,
            };
          }),
        });

        if (allPotesCheios) {
          // Se todos os potes estão cheios, priorizar vendedores que aceitam leads extras
          const vendedoresLeadsExtras = vendedoresDisponiveis.filter(
            (vendedor) => vendedor.leadsExtras === true
          );

          logger.selectionCriteria("Vendedores que aceitam leads extras", {
            total: vendedoresLeadsExtras.length,
            vendedores: vendedoresLeadsExtras.map((v) => v.nome),
          });

          // Incluir vendedores fora do horário se permitido
          if (vendedoresLeadsExtras.length === 0) {
            logger.info(
              "Nenhum vendedor dentro do horário pode receber leads extras. Buscando vendedores fora do horário..."
            );

            // Buscar vendedores fora do horário que podem receber leads extras diretamente do banco
            const vendedoresForaHorario = await prisma.vendedor.findMany({
              where: {
                ativo: true,
                foraDoHorario: true,
                leadsExtras: true,
              },
              include: {
                leads: true,
              },
            });

            vendedoresLeadsExtras.push(...vendedoresForaHorario);

            logger.selectionCriteria("Vendedores fora do horário disponíveis", {
              total: vendedoresForaHorario.length,
              vendedores: vendedoresForaHorario.map((v) => v.nome),
            });
          }

          if (vendedoresLeadsExtras.length === 0) {
            logger.error(
              "Nenhum vendedor disponível para receber leads extras",
              "Nenhum vendedor disponível"
            );
            throw new Error(
              "Nenhum vendedor disponível para receber leads extras."
            );
          }

          const menorProporcao = Math.min(
            ...vendedoresLeadsExtras.map((v) => {
              // Validar valores antes de calcular
              const leadsHoje = typeof v.leadsRecebidosHoje === 'number' ? v.leadsRecebidosHoje : 0;
              const maxLeads = (typeof v.maxLeadsDia === 'number' && v.maxLeadsDia > 0) ? 
                             v.maxLeadsDia : 10; // Valor padrão se inválido
              return leadsHoje / maxLeads;
            })
          );

          logger.selectionCriteria("Menor proporção de ocupação", {
            menorProporcao,
          });

          const vendedoresMenorProporcao = vendedoresLeadsExtras.filter((v) => {
            // Validar valores antes de filtrar
            const leadsHoje = typeof v.leadsRecebidosHoje === 'number' ? v.leadsRecebidosHoje : 0;
            const maxLeads = (typeof v.maxLeadsDia === 'number' && v.maxLeadsDia > 0) ? 
                           v.maxLeadsDia : 10; // Valor padrão se inválido
            const proporcao = leadsHoje / maxLeads;
            // Usar uma comparação com tolerância para números de ponto flutuante
            return Math.abs(proporcao - menorProporcao) < 0.001;
          });

          logger.selectionCriteria("Vendedores com menor proporção", {
            total: vendedoresMenorProporcao.length,
            vendedores: vendedoresMenorProporcao.map((v) => ({
              nome: v.nome,
              ocupacao: `${v.leadsRecebidosHoje}/${v.maxLeadsDia}`,
              proporcao: v.leadsRecebidosHoje / v.maxLeadsDia,
            })),
          });

          if (vendedoresMenorProporcao.length > 1) {
            const vendedoresComUltimoLead = await Promise.all(
              vendedoresMenorProporcao.map(async (vendedor) => {
                const ultimoLead = await prismaTransaction.lead.findFirst({
                  where: { vendedorId: vendedor.id },
                  orderBy: { createdAt: "desc" },
                });
                return {
                  ...vendedor,
                  ultimoLeadDate: ultimoLead
                    ? ultimoLead.createdAt
                    : new Date(0),
                };
              })
            );

            vendedoresComUltimoLead.sort(
              (a, b) => a.ultimoLeadDate.getTime() - b.ultimoLeadDate.getTime()
            );
            
            logger.selectionCriteria("Desempate por tempo sem receber lead", {
              vendedores: vendedoresComUltimoLead.map(v => ({
                nome: v.nome,
                ultimoLead: v.ultimoLeadDate.toISOString(),
                tempoSemLead: `${Math.round((new Date().getTime() - v.ultimoLeadDate.getTime()) / (1000 * 60))} minutos`
              }))
            });

            // Log específico para mostrar quem foi o vencedor do desempate e por quê
            const vencedor = vendedoresComUltimoLead[0];
            const segundoLugar = vendedoresComUltimoLead[1];
            
            if (segundoLugar) {
              const tempoVencedor = new Date().getTime() - vencedor.ultimoLeadDate.getTime();
              const tempoSegundoLugar = new Date().getTime() - segundoLugar.ultimoLeadDate.getTime();
              const diferencaMinutos = Math.round((tempoVencedor - tempoSegundoLugar) / (1000 * 60));
              
              logger.selectionCriteria(`Vendedor escolhido: ${vencedor.nome}`, {
                motivo: "Está há mais tempo sem receber leads",
                diferenca: `${Math.abs(diferencaMinutos)} minutos a mais sem receber leads que o próximo vendedor (${segundoLugar.nome})`
              });
            } else {
              logger.selectionCriteria(`Vendedor escolhido: ${vencedor.nome}`, {
                motivo: "Único vendedor no critério de desempate"
              });
            }

            selectedVendor = vendedoresComUltimoLead[0];
          } else {
            selectedVendor = vendedoresMenorProporcao[0];
          }
          logger.selectionCriteria("Vendedor selecionado para leads extras", {
            nome: selectedVendor.nome,
            pote: selectedVendor.maxLeadsDia,
            ocupacao: selectedVendor.leadsRecebidosHoje,
          });
        } else {
          let menorPercentual = Infinity;
          let vendedoresMenorPercentual: Vendedor[] = [];

          vendedoresDisponiveis.forEach((vendedor) => {
            // Validar valores antes de verificar se o pote está cheio
            const leadsHoje = typeof vendedor.leadsRecebidosHoje === 'number' ? vendedor.leadsRecebidosHoje : 0;
            const maxLeads = (typeof vendedor.maxLeadsDia === 'number' && vendedor.maxLeadsDia > 0) ? 
                           vendedor.maxLeadsDia : 10; // Valor padrão se inválido
                           
            if (leadsHoje < maxLeads) {
              const percentual = leadsHoje / maxLeads;
              if (percentual < menorPercentual) {
                menorPercentual = percentual;
                vendedoresMenorPercentual = [vendedor];
              } else if (Math.abs(percentual - menorPercentual) < 0.001) {
                vendedoresMenorPercentual.push(vendedor);
              }
            }
          });

          logger.selectionCriteria(
            "Vendedores com menor percentual de preenchimento",
            {
              percentual: menorPercentual,
              total: vendedoresMenorPercentual.length,
              vendedores: vendedoresMenorPercentual.map((v) => ({
                nome: v.nome,
                ocupacao: `${v.leadsRecebidosHoje}/${v.maxLeadsDia}`,
                percentual: `${Math.round(menorPercentual * 100)}%`,
              })),
            }
          );

          if (vendedoresMenorPercentual.length > 1) {
            const vendedoresComUltimoLead = await Promise.all(
              vendedoresMenorPercentual.map(async (vendedor) => {
                const ultimoLead = await prismaTransaction.lead.findFirst({
                  where: { vendedorId: vendedor.id },
                  orderBy: { createdAt: "desc" },
                });
                return {
                  ...vendedor,
                  ultimoLeadDate: ultimoLead
                    ? ultimoLead.createdAt
                    : new Date(0),
                };
              })
            );

            vendedoresComUltimoLead.sort(
              (a, b) => a.ultimoLeadDate.getTime() - b.ultimoLeadDate.getTime()
            );
            
            logger.selectionCriteria("Desempate por tempo sem receber lead", {
              vendedores: vendedoresComUltimoLead.map(v => ({
                nome: v.nome,
                ultimoLead: v.ultimoLeadDate.toISOString(),
                tempoSemLead: `${Math.round((new Date().getTime() - v.ultimoLeadDate.getTime()) / (1000 * 60))} minutos`
              }))
            });

            // Log específico para mostrar quem foi o vencedor do desempate e por quê
            const vencedor = vendedoresComUltimoLead[0];
            const segundoLugar = vendedoresComUltimoLead[1];
            
            if (segundoLugar) {
              const tempoVencedor = new Date().getTime() - vencedor.ultimoLeadDate.getTime();
              const tempoSegundoLugar = new Date().getTime() - segundoLugar.ultimoLeadDate.getTime();
              const diferencaMinutos = Math.round((tempoVencedor - tempoSegundoLugar) / (1000 * 60));
              
              logger.selectionCriteria(`Vendedor escolhido: ${vencedor.nome}`, {
                motivo: "Está há mais tempo sem receber leads",
                diferenca: `${Math.abs(diferencaMinutos)} minutos a mais sem receber leads que o próximo vendedor (${segundoLugar.nome})`
              });
            } else {
              logger.selectionCriteria(`Vendedor escolhido: ${vencedor.nome}`, {
                motivo: "Único vendedor no critério de desempate"
              });
            }

            selectedVendor = vendedoresComUltimoLead[0];
          } else {
            selectedVendor = vendedoresMenorPercentual[0];
            logger.selectionCriteria(
              "Vendedor selecionado com menor percentual",
              {
                nome: selectedVendor.nome,
                percentual: menorPercentual,
              }
            );
          }
        }
      }

      return {
        selectedVendorOwnerId: selectedVendor.ownerId,
        selectedVendorId: selectedVendor.id,
        vendedorNome: selectedVendor.nome,
        nomeAbreviado: selectedVendor.nomeAbreviado,
      };
    });
  } catch (error) {
    logger.error("Erro ao selecionar vendedor", error);
    return {
      selectedVendorOwnerId: null,
      selectedVendorId: null,
      vendedorNome: null,
      nomeAbreviado: null,
    };
  }
};

export const updateGoogleSheet = async (
  spreadsheetId: string,
  vendedorSelecionado: any
) => {
  try {
    if (vendedorSelecionado.nomeAbreviado) {
      // Definir o intervalo de tempo para 20h de ontem até 20h de hoje
      const timezone = "America/Sao_Paulo";
      const today = new Date();

      // Início do intervalo: 20h do dia anterior
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() - 1,
        20,
        0,
        0
      );

      // Fim do intervalo: 20h do dia atual
      const endOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        20,
        0,
        0
      );

      // Conversão correta para UTC
      const startOfDayUtc = new Date(
        startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
      );
      
      const endOfDayUtc = new Date(
        endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
      );
      
      const startOfDayIso = startOfDayUtc.toISOString();
      const endOfDayIso = endOfDayUtc.toISOString();

      logger.info(`Intervalo de busca para planilha: ${startOfDayIso} a ${endOfDayIso}`);

      // Buscar o vendedor com filtro de leads diretamente na consulta
      const vendedorAtualizado = await config.prisma.vendedor.findFirst({
        where: { nomeAbreviado: vendedorSelecionado.nomeAbreviado },
        include: {
          leads: {
            where: {
              AND: [
                { createdAt: { gte: startOfDayIso } },
                { createdAt: { lte: endOfDayIso } },
              ],
            },
          },
        },
      });

      if (!vendedorAtualizado) {
        logger.error(`Erro: Vendedor ${vendedorSelecionado.nomeAbreviado} não encontrado no banco de dados`, { error: "Vendedor não encontrado" });
        throw new Error("Vendedor não encontrado.");
      }

      // Usar a contagem direta dos leads filtrados
      const leadsRecebidosHoje = vendedorAtualizado.leads.length;
      
      // Para garantir que temos o valor mais atualizado, também consultar o campo leadsRecebidosHoje do vendedor
      const vendedorInfoAtualizada = await config.prisma.vendedor.findFirst({
        where: { nomeAbreviado: vendedorSelecionado.nomeAbreviado },
        select: { leadsRecebidosHoje: true }
      });
      
      // Usar o maior valor entre a contagem de leads e o campo leadsRecebidosHoje
      const valorFinalLeads = vendedorInfoAtualizada && 
                             vendedorInfoAtualizada.leadsRecebidosHoje !== null && 
                             vendedorInfoAtualizada.leadsRecebidosHoje > leadsRecebidosHoje
                               ? vendedorInfoAtualizada.leadsRecebidosHoje
                               : leadsRecebidosHoje;
      
      logger.info(`Vendedor ${vendedorSelecionado.nomeAbreviado} recebeu ${valorFinalLeads} leads hoje (banco: ${vendedorInfoAtualizada?.leadsRecebidosHoje}, contagem: ${leadsRecebidosHoje})`);
      
      let selectedVendorColumnName;
      switch (vendedorSelecionado.nomeAbreviado) {
        case "Douglas L.":
          selectedVendorColumnName = "D";
          break;
        case "Jackeline S.":
          selectedVendorColumnName = "E";
          break;
        case "Leila L.":
          selectedVendorColumnName = "F";
          break;
        case "Delita S.":
          selectedVendorColumnName = "G";
          break;
        case "Priscila C.":
          selectedVendorColumnName = "H";
          break;
        case "João B.":
          selectedVendorColumnName = "I";
          break;
        case "Amanda G.":
          selectedVendorColumnName = "J";
          break;
        case "Bruna S.":
          selectedVendorColumnName = "K";
          break;
        case "Amauri V.":
          selectedVendorColumnName = "L";
          break;
        case "Kadu F.":
          selectedVendorColumnName = "M";
          break;
        case "Vitor B.":
          selectedVendorColumnName = "N";
          break;
        case "Gustavo T.":
          selectedVendorColumnName = "O";
          break;
        case "Amanda A.":
          selectedVendorColumnName = "P";
          break;
        case "Amanda O.":
          selectedVendorColumnName = "Q";
          break;
        case "Ana F.":
          selectedVendorColumnName = "R";
          break;
        case "Ariel":
          selectedVendorColumnName = "S";
          break;
        case "Luis F.":
          selectedVendorColumnName = "T";
          break;
        default:
          logger.error(`Erro: Nome de vendedor inválido: ${vendedorSelecionado.nomeAbreviado}`, { error: "Nome de vendedor desconhecido" });
          throw new Error("Nome de vendedor inválido.");
      }
      
      logger.info(`Atualizando coluna ${selectedVendorColumnName} na planilha para o vendedor ${vendedorSelecionado.nomeAbreviado}`);
      
      await config.sheets.spreadsheets.values.update({
        spreadsheetId: spreadsheetId,
        range: `'Entrega de Leads'!${selectedVendorColumnName}7`,
        valueInputOption: "RAW",
        requestBody: {
          values: [[vendedorSelecionado.maxLeadsDia]],
        },
      });
      const rangeResponse = await config.sheets.spreadsheets.values.get({
        spreadsheetId: spreadsheetId,
        range: "'Entrega de Leads'!B:B",
      });
      const currentDateString = new Intl.DateTimeFormat("pt-BR", {
        timeZone: timezone,
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      }).format(today);

      const datesColumn = rangeResponse.data.values;
      if (!datesColumn) {
        logger.error("Coluna de datas não encontrada na planilha", { error: "Dados ausentes" });
        throw new Error("Coluna de datas não encontrada na planilha.");
      }

      let rowIndex = -1;
      for (let i = datesColumn.length - 1; i >= 0; i--) {
        const row = datesColumn[i];
        if (row && row[0]?.trim() === currentDateString) {
          rowIndex = i;
          break;
        }
      }

      if (rowIndex === -1) {
        logger.error(`Data de hoje (${currentDateString}) não encontrada na planilha`, { error: "Data não encontrada" });
        throw new Error("Data de hoje não encontrada na coluna de datas.");
      }
      
      logger.info(`Atualizando célula ${selectedVendorColumnName}${rowIndex + 1} com valor ${valorFinalLeads}`);
      
      await config.sheets.spreadsheets.values.update({
        spreadsheetId: spreadsheetId,
        range: `'Entrega de Leads'!${selectedVendorColumnName}${rowIndex + 1}`,
        valueInputOption: "RAW",
        requestBody: {
          values: [[valorFinalLeads]],
        },
      });

      // Usar logger.info em vez de sheetUpdate
      logger.info(`PLANILHA ATUALIZADA: ${vendedorSelecionado.nomeAbreviado}`, { leadsRecebidosHoje: valorFinalLeads });
    } else {
      logger.warning("Vendedor inválido ou leads não definidos", {
        nomeAbreviado: vendedorSelecionado?.nomeAbreviado
      });
    }
  } catch (error: any) {
    logger.error("Erro ao atualizar planilha do Google Sheets", error);
    throw new Error("Erro ao atualizar planilha do Google Sheets");
  }
};
export const findValueByName = (
  obj: any,
  targetFieldName: string
): string | null => {
  const queue = [obj];

  while (queue.length > 0) {
    const currentObj = queue.shift();

    for (const key in currentObj) {
      if (key === "nome" && currentObj[key] === targetFieldName) {
        if (currentObj.hasOwnProperty("valor")) {
          return currentObj.valor;
        }
      }

      if (typeof currentObj[key] === "object") {
        queue.push(currentObj[key]);
      }
    }
  }

  return null;
};

export function sentNotification(
  nomeVendedor: string,
  identificadorVendedor: string,
  mensagem: string
): void {
  logger.info(`Tentando enviar notificação para ${nomeVendedor} (ID: ${identificadorVendedor})`);
  const data = JSON.stringify({
    nomeVendedor,
    identificadorVendedor,
    mensagem,
  });
  try {
    WebSocketClient.send(data);
    logger.notification(nomeVendedor, JSON.parse(mensagem));
  } catch (error) {
    logger.error(`Falha ao enviar notificação para ${nomeVendedor}`, error);
  }
}
export const distributeLead = async (
  leadData: any,
  stageIdToMove: number,
  spreadsheetId: string,
  oldVendor?: {
    id: string | null;
    nomeAbreviado: string | null;
  }
) => {
  logger.info("Iniciando distribuição de lead");

  try {
    logger.leadReceived(leadData.id.toString(), leadData.title || 'N/A', leadData);

    const leadScore = findValueByName(leadData, "Lead Score");
    const porte = findValueByName(leadData, "porte") || "";

    function isValidJson(str: string): boolean {
      try {
        JSON.parse(str);
        return true;
      } catch {
        return false;
      }
    }
    const porteArray = isValidJson(porte) ? JSON.parse(porte) : [];

    const isLowScoreLead: boolean =
      (porteArray.length > 0 && porteArray[0] === "MEI") ||
      (leadScore !== null && leadScore !== "" && Number(leadScore) < 70);

    const normalizeText = (text: string) =>
      text.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

    const leadOrigem = normalizeText(leadData.origin?.name || "");
    const leadIndicado = leadOrigem.toLowerCase().includes("indicacao");

    logger.leadCharacteristics(
      leadData.id.toString(), 
      isLowScoreLead, 
      leadIndicado, 
      leadScore
    );

    if (oldVendor?.id) {
      logger.info(`Lead sendo redistribuído. Vendedor anterior: ${oldVendor.nomeAbreviado || 'N/A'}`);
    }

    // Definir o intervalo de tempo para 20h de ontem até 20h de hoje (para contagem)
    const timezone = "America/Sao_Paulo";
    const today = new Date();

    // Início do intervalo: 20h do dia anterior
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 1,
      20,
      0,
      0
    );

    // Fim do intervalo: 20h do dia atual
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      20,
      0,
      0
    );

    // Converte para UTC
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: timezone })
    ).toISOString();
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: timezone })
    ).toISOString();

    // Buscar todos os vendedores com seus dados ATUALIZADOS do banco
    let allVendedores: any[] = await config.prisma.vendedor.findMany({
      where: {
        ativo: true,
        ...(oldVendor?.id && { id: { not: oldVendor.id } }), // Exclui o oldVendor da busca
      },
      include: {
        leads: {
          where: {
            AND: [
              { createdAt: { gte: startOfDayUtc } },
              { createdAt: { lte: endOfDayUtc } },
            ],
          },
        },
      },
    });

    // Atualizar os contadores leadsRecebidosHoje com a contagem real
    allVendedores = await Promise.all(
      allVendedores.map(async (vendedor) => {
        const leadsCount = vendedor.leads.length;
        
        // Se a contagem de leads for diferente do valor armazenado, atualizar no banco
        if (leadsCount !== vendedor.leadsRecebidosHoje) {
          logger.info(`Atualizando contador para ${vendedor.nome}: ${vendedor.leadsRecebidosHoje} → ${leadsCount} leads`);
          
          // Atualizar no banco
          await config.prisma.vendedor.update({
            where: { id: vendedor.id },
            data: { leadsRecebidosHoje: leadsCount }
          });
          
          // Atualizar no objeto que será retornado
          vendedor.leadsRecebidosHoje = leadsCount;
        }
        return vendedor;
      })
    );

    // Log para debug mostrando os valores iniciais de cada vendedor
    logger.info("Valores de vendedores antes da filtragem e seleção:", 
      allVendedores.map(vendedor => ({
        nome: vendedor.nome,
        leads_hoje: vendedor.leadsRecebidosHoje,
        pote: vendedor.maxLeadsDia,
        porcentagem: `${Math.round((vendedor.leadsRecebidosHoje / vendedor.maxLeadsDia) * 100)}%`
      }))
    );

    logger.info(`Vendedores obtidos do banco: ${allVendedores.length}`);

    allVendedores = await filterAndSortVendedores(allVendedores);

    logger.availableVendors(allVendedores);

    const selectedVendor = await selectVendor(
      allVendedores,
      leadIndicado,
      isLowScoreLead
    );

    if (!selectedVendor || !selectedVendor.selectedVendorId) {
      logger.fallback(leadData.id.toString(), "Nenhum vendedor selecionado");

      const leadFallback = await createLead(
        leadData.id.toString(),
        leadData.title,
        undefined, // Vendedor não atribuído
        true, // Flag de fallback
        Number(leadScore),
        leadOrigem,
        leadIndicado,
        isLowScoreLead
      );

      return {
        message: "Nenhum vendedor disponível. Lead marcado como em fallback.",
        lead: leadFallback,
      };
    }

    const {
      selectedVendorId,
      selectedVendorOwnerId,
      vendedorNome,
      nomeAbreviado,
    } = selectedVendor;

    logger.vendorSelected(leadData.id.toString(), {
      id: selectedVendorId,
      nome: vendedorNome,
      nomeAbreviado: nomeAbreviado
    }, [
      leadIndicado ? "Lead indicado" : "Lead comum",
      isLowScoreLead ? "Lead de baixo score" : "Lead de alto score"
    ]);

    if (selectedVendorOwnerId) {
      try {
        await updateLeadInPipeRun(
          leadData.id.toString(),
          stageIdToMove,
          selectedVendorOwnerId
        );
        logger.crmUpdate(leadData.id.toString(), selectedVendorOwnerId);
      } catch (error) {
        logger.error("Erro ao atualizar o lead no PipeRun", error);
      }
    }

    try {
      // Após atualizar o lead no DB, vamos obter o valor real de leadsRecebidosHoje
      const vendedorAtualizado = await config.prisma.vendedor.findUnique({
        where: { id: selectedVendorId },
        include: {
          leads: {
            where: {
              AND: [
                { createdAt: { gte: startOfDayUtc } },
                { createdAt: { lte: endOfDayUtc } },
              ],
            },
          },
        }
      });

      // Criar o lead no banco de dados
      const lead = await createLead(
        leadData.id.toString(),
        leadData.title,
        selectedVendorId || "",
        false,
        Number(leadScore),
        leadOrigem,
        leadIndicado,
        isLowScoreLead
      );
      
      logger.info(`Lead criado no banco: ${lead.id}`);

      let leadsReais = 0;
      
      if (vendedorAtualizado) {
        leadsReais = vendedorAtualizado.leads.length + 1; // +1 para incluir o lead que acabamos de criar
        
        // Apenas registrar no log sem atualizar o banco
        logger.info(`Contagem calculada para ${nomeAbreviado}: ${leadsReais} leads (contagem real)`);
        
        // Atualizar a planilha com o valor real de leads
        await updateGoogleSheet(spreadsheetId, {
          nomeAbreviado,
          maxLeadsDia: vendedorAtualizado.maxLeadsDia,
          leadsRecebidosHoje: leadsReais
        });

        logger.info(`Planilha atualizada para ${nomeAbreviado} com contagem real: ${leadsReais} leads.`);
      } else {
        // Caso não encontre o vendedor, usar a abordagem normal
        await updateGoogleSheet(spreadsheetId, {
          nomeAbreviado,
          maxLeadsDia: allVendedores.find(v => v.id === selectedVendorId)?.maxLeadsDia,
        });
        
        logger.info(`Planilha atualizada para ${nomeAbreviado} sem contagem real disponível.`);
      }
      
      // Enviar notificação
      const messageNotification = {
        type: "NOVO LEAD",
        title: leadData.title,
        name: nomeAbreviado,
        leadScore: leadScore,
        crmLink: `https://app.pipe.run/pipeline/gerenciador/visualizar/${leadData.id}`,
      };

      if (selectedVendorOwnerId && nomeAbreviado) {
        try {
          sentNotification(
            nomeAbreviado,
            selectedVendorOwnerId.toString(),
            JSON.stringify(messageNotification)
          );
          logger.notification(nomeAbreviado, messageNotification);
        } catch (error) {
          logger.error(`Falha ao enviar notificação para ${nomeAbreviado}`, error);
        }
      }

      if (oldVendor) {
        logger.info(`Lead re-delegado de ${oldVendor.nomeAbreviado || 'N/A'} para ${nomeAbreviado || 'N/A'}`);
      } else {
        logger.distributionComplete(leadData.id.toString(), nomeAbreviado || 'N/A');
      }

      return {
        message: oldVendor
          ? `Lead re-delegado de ${oldVendor.nomeAbreviado} para ${nomeAbreviado} com sucesso.`
          : `Lead registrado com sucesso e atribuído ao vendedor ${nomeAbreviado}`,
        lead,
        selectedVendorOwnerId,
        selectedVendorId
      };
    } catch (error) {
      logger.error("Erro ao criar o lead ou atualizar planilha", error);
      throw error;
    }
  } catch (error) {
    logger.error("Erro inesperado durante a distribuição do lead", error);
    throw error;
  }
};

// Adicionar nova função para buscar dados do lead no CRM
export const getLeadFromPipeRun = async (leadId: string) => {
  const apiUrl = `https://api.pipe.run/v1/deals/${leadId}?with=customFields,origin,owner`;
  const headers = {
    accept: "application/json",
    token: process.env.CRM_TOKEN,
  };

  try {
    const response = await axios.get(apiUrl, { headers });
    return response.data.data;
  } catch (error) {
    console.error(`[ERROR] Erro ao buscar lead ${leadId} no PipeRun:`, error);
    return null;
  }
};

export const redistributeFallbackLeads = async () => {
  logger.info("Iniciando redistribuição de leads em fallback");
  try {
    const fallbackLeads = await config.prisma.lead.findMany({
      where: {
        emFallback: true,
        vendedorId: null,
      },
    });

    logger.info(`${fallbackLeads.length} leads encontrados em fallback para redistribuição`);

    for (const lead of fallbackLeads) {
      try {
        logger.info(`Processando lead ID: ${lead.crmId}`);
        const pipeRunLead = await getLeadFromPipeRun(lead.crmId!);
        
        if (!pipeRunLead) {
          logger.warning(`Lead ${lead.crmId} não encontrado no PipeRun. Pulando...`);
          continue;
        }

        const crm_owner_id = pipeRunLead.owner_id;

        if (crm_owner_id) {
          // Buscar vendedor pelo owner_id do CRM
          const vendedor = await config.prisma.vendedor.findFirst({
            where: { ownerId: crm_owner_id },
          });

          if (vendedor) {
            logger.info(`Lead ${lead.crmId} já está atribuído ao vendedor ${vendedor.nome} no CRM`);

            // Definir o intervalo de tempo para contagem correta
            const timezone = "America/Sao_Paulo";
            const today = new Date();
            
            // Início e fim do intervalo (20h de ontem até 20h de hoje)
            const startOfDay = new Date(
              today.getFullYear(),
              today.getMonth(), 
              today.getDate() - 1,
              20, 0, 0
            );
            
            const endOfDay = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate(),
              20, 0, 0
            );
            
            // Conversão correta para UTC
            const startOfDayUtc = new Date(
              startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
            );
            
            const endOfDayUtc = new Date(
              endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
            );
            
            const startOfDayIso = startOfDayUtc.toISOString();
            const endOfDayIso = endOfDayUtc.toISOString();
            
            // Atualizar lead no nosso sistema dentro de uma transação
            await config.prisma.$transaction(async (prisma) => {
              // Atualizar o lead
              await prisma.lead.update({
                where: { id: lead.id },
                data: {
                  emFallback: false,
                  vendedorId: vendedor.id,
                  tentativasDist: {
                    increment: 1,
                  },
                },
              });

              // Buscar o vendedor com contagem real de leads no intervalo correto
              const vendedorComLeads = await prisma.vendedor.findUnique({
                where: { id: vendedor.id },
                include: {
                  leads: {
                    where: {
                      AND: [
                        { createdAt: { gte: startOfDayIso } },
                        { createdAt: { lte: endOfDayIso } },
                      ],
                    },
                  },
                },
              });
              
              if (vendedorComLeads) {
                // O lead que acabamos de associar ainda não está incluído na contagem
                const leadsCount = vendedorComLeads.leads.length + 1;
                
                // Atualizar o contador com o valor real
                await prisma.vendedor.update({
                  where: { id: vendedor.id },
                  data: { leadsRecebidosHoje: leadsCount }
                });
                
                logger.info(`Contador atualizado para ${vendedor.nomeAbreviado}: ${leadsCount} leads (baseado na contagem real)`);
              }
            });

            // Atualizar planilha
            await updateGoogleSheet(spreadsheetId, {
              nomeAbreviado: vendedor.nomeAbreviado,
              maxLeadsDia: vendedor.maxLeadsDia,
            });

            logger.distributionComplete(lead.crmId || 'N/A', vendedor.nomeAbreviado || 'N/A');
          } else {
            logger.warning(`Vendedor com ownerId ${crm_owner_id} não encontrado no sistema interno`);
          }
        } else {
          logger.info(`Lead ${lead.crmId} não possui vendedor no CRM. Iniciando redistribuição...`);

          const leadData = {
            id: lead.crmId!,
            title: lead.nome!,
            origem: lead.origem || "Desconhecida",
            indicado: lead.indicado || false,
            leadsLowScore: lead.leadsLowScore || false,
          };
          
          logger.leadReceived(lead.crmId || 'N/A', lead.nome || 'N/A', leadData);
          logger.leadCharacteristics(
            lead.crmId || 'N/A', 
            lead.leadsLowScore || false, 
            lead.indicado || false, 
            lead.score || 'N/A'
          );

          // A função distributeLead já faz a contagem correta e atualiza a planilha
          const distributionResult = await distributeLead(
            leadData,
            432437,
            spreadsheetId
          );

          if (distributionResult?.selectedVendorId) {
            // Agora precisamos atualizar o contador do vendedor aqui

            // Definir o intervalo de tempo para contagem correta
            const timezone = "America/Sao_Paulo";
            const today = new Date();
            
            // Início e fim do intervalo (20h de ontem até 20h de hoje)
            const startOfDay = new Date(
              today.getFullYear(),
              today.getMonth(), 
              today.getDate() - 1,
              20, 0, 0
            );
            
            const endOfDay = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate(),
              20, 0, 0
            );
            
            // Conversão correta para UTC
            const startOfDayUtc = new Date(
              startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
            );
            
            const endOfDayUtc = new Date(
              endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
            );
            
            const startOfDayIso = startOfDayUtc.toISOString();
            const endOfDayIso = endOfDayUtc.toISOString();

            // Buscar o vendedor com seus leads para calcular a contagem correta
            const vendedorAtualizado = await config.prisma.vendedor.findUnique({
              where: { id: distributionResult.selectedVendorId },
              include: {
                leads: {
                  where: {
                    AND: [
                      { createdAt: { gte: startOfDayIso } },
                      { createdAt: { lte: endOfDayIso } },
                    ],
                  },
                },
              },
            });
            
            if (vendedorAtualizado) {
              // O lead que acabamos de criar ainda não está incluído na contagem
              const leadsCount = vendedorAtualizado.leads.length + 1;
              
              // Atualizar o contador com o valor real
              await config.prisma.vendedor.update({
                where: { id: distributionResult.selectedVendorId },
                data: { leadsRecebidosHoje: leadsCount }
              });
              
              logger.info(`Contador atualizado para vendedor ID ${distributionResult.selectedVendorId}: ${leadsCount} leads (baseado na contagem real)`);
            }

            // Adicionar atualização do status de fallback
            await config.prisma.lead.update({
              where: { id: lead.id },
              data: {
                emFallback: false,
                vendedorId: distributionResult.selectedVendorId
              }
            });
            
            logger.distributionComplete(
              lead.crmId || 'N/A',
              distributionResult.message || 'Vendedor desconhecido'
            );
          } else {
            logger.fallback(lead.crmId || 'N/A', "Falha na redistribuição");
          }
        }
      } catch (error) {
        logger.error(`Erro ao processar lead ID: ${lead.crmId}`, error);
      }
    }

    logger.info("Processo de redistribuição concluído com sucesso");
  } catch (error) {
    logger.error("Erro no processo de redistribuição", error);
  }
};
