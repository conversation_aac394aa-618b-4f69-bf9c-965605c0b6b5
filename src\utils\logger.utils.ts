import fs from 'fs';
import path from 'path';

// Pasta para armazenar os logs
const LOG_DIR = path.join(process.cwd(), 'logs');

// Certificar que a pasta de logs existe
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR);
}

// Caminho do arquivo de log de distribuição
const DISTRIBUTION_LOG = path.join(LOG_DIR, 'distribution.log');

// Estatísticas diárias em tempo real
export const dailyStats = {
  totalLeads: 0,
  distributedLeads: 0,
  fallbackLeads: 0,
  lowScoreLeads: 0,
  indicatedLeads: 0,
  vendedoresCount: {} as Record<string, number>,
  startTime: new Date(),
  isInPm2: process.env.PM2_HOME !== undefined
};

// Classe para gerenciar os logs de cada lead
class LeadLogger {
  leadId: string;
  leadTitle: string;
  logEntries: string[] = [];
  createdAt: Date;
  isComplete: boolean = false;
  score: string | number | null = null;
  isIndicado: boolean = false;
  isLowScore: boolean = false;
  selectedVendor: string | null = null;
  selectionReason: string | null = null; // Motivo da seleção
  
  constructor(leadId: string, leadTitle: string) {
    this.leadId = leadId;
    this.leadTitle = leadTitle;
    this.createdAt = new Date();
  }
  
  addEntry(prefix: string, message: string, emoji?: string) {
    this.logEntries.push(`    ${prefix} ${emoji ? emoji + ' ' : ''}${message}`);
  }
  
  setCharacteristics(score: string | number | null, isIndicado: boolean, isLowScore: boolean) {
    this.score = score;
    this.isIndicado = isIndicado;
    this.isLowScore = isLowScore;
    
    // Atualizar estatísticas
    if (isLowScore) dailyStats.lowScoreLeads++;
    if (isIndicado) dailyStats.indicatedLeads++;
  }
  
  setSelectedVendor(vendorName: string, reason?: string) {
    this.selectedVendor = vendorName;
    this.selectionReason = reason || null;
    
    // Atualizar estatísticas
    dailyStats.distributedLeads++;
    dailyStats.vendedoresCount[vendorName] = (dailyStats.vendedoresCount[vendorName] || 0) + 1;
  }
  
  markComplete() {
    this.isComplete = true;
  }
  
  // Retorna o cabeçalho com informações do lead
  getHeader() {
    const timestamp = new Intl.DateTimeFormat('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(this.createdAt).replace(/\//g, '-');
    
    const scoreText = this.score || 'N/A';
    const indicadoText = this.isIndicado ? 'SIM' : 'NÃO';
    const lowScoreText = this.isLowScore ? 'SIM' : 'NÃO';
    
    return `[${timestamp}] | Lead #${this.leadId} | ${this.leadTitle} | Score: ${scoreText} | Indicado: ${indicadoText} | LowScore: ${lowScoreText}`;
  }
  
  // Versão completa para o arquivo de log
  toString() {
    let output = this.getHeader() + '\n';
    
    for (const entry of this.logEntries) {
      output += entry + '\n';
    }
    
    return output;
  }
  
  // Versão compacta para o console no PM2
  toConsoleString() {
    const statusEmoji = this.selectedVendor ? '✅' : this.isComplete ? '⚡' : '⏳';
    const vendorInfo = this.selectedVendor ? `→ ${this.selectedVendor}` : 'Processando...';
    
    return `${statusEmoji} Lead #${this.leadId} | ${this.leadTitle} | ${vendorInfo}`;
  }
  
  // Versão de resumo final para o console
  toSummaryString() {
    const scoreType = this.isLowScore ? 'BAIXO SCORE' : this.isIndicado ? 'INDICADO' : 'NORMAL';
    const status = this.selectedVendor 
      ? `✅ ATRIBUÍDO: ${this.selectedVendor}${this.selectionReason ? ` (${this.selectionReason})` : ''}`
      : '⚡ FALLBACK';
    
    // Extrair informações de filtro dos logs entries
    let filtros: string[] = [];
    let criteriosDecisivos: string[] = [];
    let vendedoresDisponiveis: number = 0;
    let top3Vendedores: string = "";
    
    // Analisar entradas de log para extrair informações relevantes
    for (const entry of this.logEntries) {
      if (entry.includes('Vendedores disponíveis:')) {
        const match = entry.match(/Vendedores disponíveis: (\d+)(?:\s*\|\s*Top 3: (.*))?/);
        if (match) {
          vendedoresDisponiveis = parseInt(match[1]);
          if (match[2]) {
            top3Vendedores = match[2];
          }
        }
      }
      
      // Capturar filtros aplicados
      if (entry.includes('Filtro por tipo:') || entry.includes('Seleção por ocupação:')) {
        filtros.push(entry.replace(/.*?└─\s*🧩\s*/, '').trim());
      }
      
      // Capturar critérios decisivos
      if (entry.includes('Critério decisivo') || entry.includes('Desempate por tempo')) {
        criteriosDecisivos.push(entry.replace(/.*?└─\s*🧩\s*/, '').trim());
      }
      
      // Capturar resumo de seleção
      if (entry.includes('RESUMO DE SELEÇÃO')) {
        const resumoData = entry.replace(/.*?├─\s*📊\s*RESUMO DE SELEÇÃO/, '').trim();
        if (resumoData && resumoData !== '|') {
          criteriosDecisivos.push(resumoData);
        }
      }
    }

    // Montar a string de resumo
    let summary = `\n────────── RESUMO DA DISTRIBUIÇÃO ──────────
    Lead #${this.leadId} | ${this.leadTitle}
    Score: ${this.score || 'N/A'} | Tipo: ${scoreType}
    Status: ${status}`;
    
    // Adicionar informações de filtro e ordenação se disponíveis
    if (vendedoresDisponiveis > 0) {
      summary += `\n    Vendedores disponíveis: ${vendedoresDisponiveis}`;
      if (top3Vendedores) {
        summary += `\n    Top candidatos: ${top3Vendedores}`;
      }
    }
    
    if (filtros.length > 0) {
      summary += `\n    Filtros: ${filtros.join(' | ')}`;
    }
    
    if (criteriosDecisivos.length > 0) {
      summary += `\n    Critérios decisivos: ${criteriosDecisivos.join(' | ')}`;
    }
    
    summary += `\n    ───────────────────────────────────────────`;
    
    return summary;
  }
}

// Cache de logs de leads em processamento
const activeLeadLogs = new Map<string, LeadLogger>();

/**
 * Formata a data atual para o fuso horário de São Paulo
 * @returns Timestamp formatado
 */
function getFormattedTimestamp() {
  const now = new Date();
  // Formatar com Intl.DateTimeFormat
  const formattedDate = new Intl.DateTimeFormat('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(now).replace(',', '').replace(/\//g, '-');
  
  // Adicionar milissegundos manualmente
  const milliseconds = now.getMilliseconds().toString().padStart(3, '0');
  
  return `${formattedDate}.${milliseconds}`;
}

/**
 * Determina se deve mostrar logs detalhados no console
 */
function shouldShowDetailedLogs() {
  // Em ambiente de desenvolvimento, mostrar todos os logs
  // Em PM2, mostrar apenas os logs resumidos
  return !dailyStats.isInPm2 || process.env.LOG_LEVEL === 'debug';
}

/**
 * Gera uma entrada de log para um lead específico
 */
function logForLead(leadId: string, prefix: string, message: string, emoji?: string, data?: any) {
  if (!activeLeadLogs.has(leadId)) {
    // Se não conseguirmos identificar o lead, usar o formato antigo
    const timestamp = getFormattedTimestamp();
    const formattedMessage = `${timestamp} ${emoji || ''} ${message}`;
    console.log(formattedMessage);
    if (data) {
      const logEntry = `${formattedMessage}\n${JSON.stringify(data, null, 2)}\n`;
      fs.appendFileSync(DISTRIBUTION_LOG, logEntry + '\n');
    } else {
      fs.appendFileSync(DISTRIBUTION_LOG, formattedMessage + '\n');
    }
    return;
  }
  
  const leadLogger = activeLeadLogs.get(leadId)!;
  leadLogger.addEntry(prefix, message, emoji);
  
  // Se tem dados adicionais, registrar como comentário
  if (data) {
    const dataStr = JSON.stringify(data, null, 2);
    const dataLines = dataStr.split('\n');
    for (const line of dataLines) {
      leadLogger.addEntry('│   ', line);
    }
  }
  
  // Imprimir no console apenas se for um log importante ou estamos em modo detalhado
  const isImportantLog = prefix.includes('🎯') || prefix.includes('└─');
  if (isImportantLog) {
    if (shouldShowDetailedLogs()) {
      // Em modo detalhado, mostrar o log completo
      console.log(leadLogger.toString());
    } else {
      // Em PM2, mostrar apenas o status atual
      console.log(leadLogger.toConsoleString());
    }
  } else if (shouldShowDetailedLogs()) {
    // Se estiver em modo detalhado, mostrar a linha individual
    console.log(`${getFormattedTimestamp()} ${emoji || ''} ${message}`);
  }
}

/**
 * Adiciona um log estruturado ao console e ao arquivo que não está associado a um lead específico
 */
function log(type: string, message: string, data?: any) {
  const timestamp = getFormattedTimestamp();
  const formattedMessage = `${timestamp} ${type} ${message}`;
  
  // Log para o console - apenas se não estiver em PM2 ou for um log importante
  if (shouldShowDetailedLogs() || type.includes('❌') || type.includes('⚠️')) {
    console.log(formattedMessage);
  }
  
  // Log para o arquivo
  if (data) {
    const logEntry = `${formattedMessage}\n${JSON.stringify(data, null, 2)}\n`;
    fs.appendFileSync(DISTRIBUTION_LOG, logEntry + '\n');
  } else {
    fs.appendFileSync(DISTRIBUTION_LOG, formattedMessage + '\n');
  }
}

/**
 * Finaliza o processamento de um lead, salvando o log completo no arquivo
 */
function completeLeadLog(leadId: string) {
  if (!activeLeadLogs.has(leadId)) return;
  
  const leadLogger = activeLeadLogs.get(leadId)!;
  leadLogger.markComplete();
  
  // Imprimir resumo final no console (sempre, mesmo em PM2)
  console.log(leadLogger.toSummaryString());
  
  // Salvar o log completo no arquivo
  fs.appendFileSync(DISTRIBUTION_LOG, leadLogger.toString() + '\n\n');
  
  // Remover do cache
  activeLeadLogs.delete(leadId);
}

export const logger = {
  // Log quando um lead é recebido
  leadReceived: (leadId: string, leadTitle: string, leadData: any) => {
    // Incrementar contador de leads
    dailyStats.totalLeads++;
    
    // Criar novo logger para este lead
    const leadLogger = new LeadLogger(leadId, leadTitle);
    activeLeadLogs.set(leadId, leadLogger);
    
    // Adicionar primeira entrada
    leadLogger.addEntry('├─', 'RECEBENDO LEAD...', '📋');
    
    // Imprimir data/hora
    const dateTime = new Intl.DateTimeFormat('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date());
    leadLogger.addEntry('├─', `Data/Hora: ${dateTime}`, '📅');
    
    // Log no console inicial - sempre mostrar este log
    console.log(`${getFormattedTimestamp()} 🔔 Novo lead recebido: #${leadId} - ${leadTitle}`);
    
    // Criar metadados para o arquivo de log
    const metadata = {
      id: leadId,
      title: leadTitle,
      score: leadData.score || 'N/A',
      origem: leadData.origem || 'N/A',
      data: leadData
    };
    
    // Apenas salvar no arquivo, não mostrar no console para não duplicar
    fs.appendFileSync(
      DISTRIBUTION_LOG, 
      `${getFormattedTimestamp()} 🔔 LEAD RECEBIDO: #${leadId} - ${leadTitle}\n${JSON.stringify(metadata, null, 2)}\n\n`
    );
  },
  
  // Log de características do lead
  leadCharacteristics: (leadId: string, isLowScore: boolean, isIndicado: boolean, score: string | number | null) => {
    if (activeLeadLogs.has(leadId)) {
      const leadLogger = activeLeadLogs.get(leadId)!;
      leadLogger.setCharacteristics(score, isIndicado, isLowScore);
      
      const scoreEmoji = isLowScore ? '📉' : '📈';
      leadLogger.addEntry('├─', `Score: ${score || 'N/A'} | Indicado: ${isIndicado ? 'SIM' : 'NÃO'} | LowScore: ${isLowScore ? 'SIM' : 'NÃO'}`, '📊');
    } else {
      // Fallback para o formato antigo
      const scoreEmoji = isLowScore ? '📉' : '📈';
      log(scoreEmoji, `LEAD #${leadId} - Score: ${score || 'N/A'} | Indicado: ${isIndicado ? 'SIM' : 'NÃO'} | Low Score: ${isLowScore ? 'SIM' : 'NÃO'}`);
    }
  },
  
  // Log de vendedores disponíveis
  availableVendors: (vendedores: any[]) => {
    if (vendedores.length === 0) return;
    
    // Assumindo que todos os vendedores são para o mesmo lead, pegamos o leadId do primeiro vendedor que tenha leads
    const anyVendorWithLeads = vendedores.find(v => v.leads && v.leads.length > 0);
    const leadId = anyVendorWithLeads?.leads[0]?.crmId;
    
    if (leadId && activeLeadLogs.has(leadId)) {
      const leadLogger = activeLeadLogs.get(leadId)!;
      
      // Criar um resumo mais conciso para o PM2
      let detalhesPrincipais = '';
      
      // Top 3 vendedores com menor ocupação
      const top3 = [...vendedores]
        .sort((a, b) => {
          const percentageA = (a.leadsRecebidosHoje || 0) / (a.maxLeadsDia || 1);
          const percentageB = (b.leadsRecebidosHoje || 0) / (b.maxLeadsDia || 1);
          return percentageA - percentageB;
        })
        .slice(0, 3);
      
      if (top3.length > 0) {
        detalhesPrincipais = ` | Top 3: ${top3.map(v => 
          `${v.nomeAbreviado || v.nome} (${v.leadsRecebidosHoje || 0}/${v.maxLeadsDia || 'N/A'})`
        ).join(', ')}`;
      }
      
      leadLogger.addEntry('├─', `Vendedores disponíveis: ${vendedores.length}${detalhesPrincipais}`, '👨‍💼');
      
      // Dados para o arquivo de log
      const vendoresData = vendedores.map(v => ({
        nome: v.nome,
        pote: v.maxLeadsDia,
        leads_hoje: v.leadsRecebidosHoje,
        horario: `${v.horarioEntrada}-${v.horarioSaida}`,
        aceita_lowscore: v.leadsLowScore,
        aceita_indicado: v.leadsIndicados,
        aceita_extra: v.leadsExtras,
        fora_horario: v.foraDoHorario,
        // Adicionar porcentagem de ocupação para fácil visualização
        ocupacao: `${Math.round((v.leadsRecebidosHoje || 0) / (v.maxLeadsDia || 1) * 100)}%`
      }));
      
      // Salvar no arquivo
      fs.appendFileSync(
        DISTRIBUTION_LOG, 
        `${getFormattedTimestamp()} 👨‍💼 VENDEDORES DISPONÍVEIS: ${vendedores.length}\n${JSON.stringify(vendoresData, null, 2)}\n\n`
      );
    } else {
      // Fallback para o formato antigo
      log('👨‍💼', `VENDEDORES DISPONÍVEIS: ${vendedores.length}`, 
        vendedores.map(v => ({
          nome: v.nome,
          pote: v.maxLeadsDia,
          leads_hoje: v.leadsRecebidosHoje,
          horario: `${v.horarioEntrada}-${v.horarioSaida}`,
          aceita_lowscore: v.leadsLowScore,
          aceita_indicado: v.leadsIndicados,
          aceita_extra: v.leadsExtras,
          fora_horario: v.foraDoHorario,
          // Adicionar porcentagem de ocupação
          ocupacao: `${Math.round((v.leadsRecebidosHoje || 0) / (v.maxLeadsDia || 1) * 100)}%`
        }))
      );
    }
  },
  
  // Log de seleção de vendedor com critérios
  vendorSelected: (leadId: string, vendedor: any, criterios: string[]) => {
    const criteriosFormatados = criterios.length > 0 
      ? `[${criterios.join(', ')}]`
      : 'Seleção regular';
    
    if (activeLeadLogs.has(leadId)) {
      const leadLogger = activeLeadLogs.get(leadId)!;
      const vendorName = vendedor.nome || vendedor.nomeAbreviado;
      
      // Armazenar o vendedor selecionado (sem sobrescrever o motivo)
      // Usamos dois parâmetros vazios para manter o motivo atual se já foi definido
      if (!leadLogger.selectedVendor) {
        leadLogger.setSelectedVendor(vendorName);
      }
      
      leadLogger.addEntry('└─', `VENDEDOR SELECIONADO: ${vendorName} ✅`, '🎯');
      
      // Dados para o arquivo de log
      const vendorData = {
        vendedor: {
          id: vendedor.id,
          nome: vendedor.nome,
          nomeAbreviado: vendedor.nomeAbreviado,
          pote: vendedor.maxLeadsDia,
          leads_hoje: vendedor.leadsRecebidosHoje,
          ocupacao: `${vendedor.leadsRecebidosHoje}/${vendedor.maxLeadsDia} (${Math.round((vendedor.leadsRecebidosHoje / vendedor.maxLeadsDia) * 100)}%)`,
        },
        criterios_selecao: criterios
      };
      
      // Salvar no arquivo
      fs.appendFileSync(
        DISTRIBUTION_LOG, 
        `${getFormattedTimestamp()} 🎯 SELEÇÃO PARA LEAD #${leadId}: ${vendorName} - ${criteriosFormatados}\n${JSON.stringify(vendorData, null, 2)}\n\n`
      );
    } else {
      // Fallback para o formato antigo
      const vendorName = vendedor.nome || vendedor.nomeAbreviado;
      
      // Atualizar estatísticas
      dailyStats.distributedLeads++;
      dailyStats.vendedoresCount[vendorName] = (dailyStats.vendedoresCount[vendorName] || 0) + 1;
      
      log('🎯', `SELEÇÃO PARA LEAD #${leadId}: ${vendorName} - ${criteriosFormatados}`, {
        vendedor: {
          id: vendedor.id,
          nome: vendedor.nome,
          nomeAbreviado: vendedor.nomeAbreviado,
          pote: vendedor.maxLeadsDia,
          leads_hoje: vendedor.leadsRecebidosHoje,
          ocupacao: `${vendedor.leadsRecebidosHoje}/${vendedor.maxLeadsDia} (${Math.round((vendedor.leadsRecebidosHoje / vendedor.maxLeadsDia) * 100)}%)`,
        },
        criterios_selecao: criterios
      });
    }
  },
  
  // Log de critério específico de seleção
  selectionCriteria: (criterio: string, detalhes: any) => {
    // Encontrar um leadId nas strings de detalhes, se existir
    let leadId = '';
    if (detalhes && typeof detalhes === 'object') {
      // Procurar em vendedores se existirem
      if (detalhes.vendedores && Array.isArray(detalhes.vendedores)) {
        for (const vendedor of detalhes.vendedores) {
          if (vendedor && vendedor.leads && vendedor.leads.length > 0) {
            leadId = vendedor.leads[0].crmId;
            break;
          }
        }
      }
    }
    
    // Transformar o critério em um formato mais amigável para o PM2
    let criterioFormatado = criterio;
    
    // Simplificar mensagens comuns
    if (criterio.includes('Vendedor escolhido')) {
      if (detalhes && detalhes.motivo) {
        criterioFormatado = `Critério decisivo: ${detalhes.motivo}`;
      }
    } else if (criterio.includes('Desempate')) {
      criterioFormatado = 'Desempate por tempo sem leads';
    } else if (criterio.includes('Filtro por tipo de lead')) {
      // Contar quantos vendedores ficaram após o filtro
      const antesCount = detalhes?.antes || 0;
      const depoisCount = detalhes?.depois || 0;
      criterioFormatado = `Filtro por tipo: ${depoisCount}/${antesCount} vendedores`;
    } else if (criterio.includes('com menor proporção')) {
      const vendedoresCount = detalhes?.vendedores?.length || 0;
      criterioFormatado = `Seleção por ocupação: ${vendedoresCount} candidatos`;
    }
    
    if (leadId && activeLeadLogs.has(leadId)) {
      logForLead(leadId, '│   └─', `${criterioFormatado}`, '🧩', detalhes);
    } else {
      // Fallback para o formato antigo
      log('🧩', `CRITÉRIO: ${criterio}`, detalhes);
    }
  },
  
  // Log de verificação de intervalo entre leads
  intervalCheck: (vendedor: any, isAvailable: boolean, lastLeadTime?: Date, interval?: number) => {
    const status = isAvailable ? 'DISPONÍVEL' : 'INDISPONÍVEL';
    const detalhes = lastLeadTime ? {
      vendedor: vendedor.nome,
      ultimo_lead: lastLeadTime,
      intervalo_minutos: interval,
      disponivel: isAvailable
    } : { disponivel: isAvailable };
    
    // Encontrar um leadId nos leads do vendedor, se existir
    let leadId = '';
    if (vendedor && vendedor.leads && vendedor.leads.length > 0) {
      leadId = vendedor.leads[0].crmId;
    }
    
    if (leadId && activeLeadLogs.has(leadId)) {
      const statusIcon = isAvailable ? '🟢 Disponível:' : '🟡 Intervalo ativo:';
      logForLead(leadId, '│   ├─', `${statusIcon} ${vendedor.nome}`, '⏳', detalhes);
    } else {
      // Fallback para o formato antigo
      log('⏱️', `INTERVALO ${status}: ${vendedor.nome}`, detalhes);
    }
  },
  
  // Log de operação de fallback
  fallback: (leadId: string, motivo: string) => {
    // Atualizar estatísticas
    dailyStats.fallbackLeads++;
    
    if (activeLeadLogs.has(leadId)) {
      logForLead(leadId, '└─', `FALLBACK: ${motivo}`, '⚡');
      completeLeadLog(leadId);
    } else {
      // Fallback para o formato antigo
      log('⚡', `FALLBACK PARA LEAD #${leadId}: ${motivo}`);
    }
  },
  
  // Log de verificação de horário
  timeCheck: (vendedor: any, dentroHorario: boolean) => {
    const status = dentroHorario ? 'DENTRO' : 'FORA';
    
    // Encontrar um leadId nos leads do vendedor, se existir
    let leadId = '';
    if (vendedor && vendedor.leads && vendedor.leads.length > 0) {
      leadId = vendedor.leads[0].crmId;
    }
    
    if (leadId && activeLeadLogs.has(leadId)) {
      const statusIcon = dentroHorario ? '✅ Dentro do horário:' : '❌ Fora do horário:';
      logForLead(leadId, '│   ├─', `${statusIcon} ${vendedor.nome} (${vendedor.horarioEntrada}-${vendedor.horarioSaida})`, '🕒');
    } else {
      // Fallback para o formato antigo
      log('🕒', `HORÁRIO ${status}: ${vendedor.nome} (${vendedor.horarioEntrada}-${vendedor.horarioSaida})`);
    }
  },
  
  // Log de notificação enviada
  notification: (vendedorName: string, message: any) => {
    // Tentar encontrar o leadId na mensagem
    let leadId = '';
    if (message && typeof message === 'object' && message.crmLink) {
      const matches = message.crmLink.match(/\/(\d+)$/);
      if (matches && matches[1]) {
        leadId = matches[1];
      }
    }
    
    if (leadId && activeLeadLogs.has(leadId)) {
      logForLead(leadId, '├─', `Notificação enviada para: ${vendedorName}`, '📱', message);
    } else {
      // Fallback para o formato antigo
      log('📱', `NOTIFICAÇÃO PARA: ${vendedorName}`, message);
    }
  },
  
  // Log de atualização do CRM
  crmUpdate: (leadId: string, vendedorId: number | string | null) => {
    if (activeLeadLogs.has(leadId)) {
      logForLead(leadId, '├─', `CRM atualizado com sucesso (vendedorId: ${vendedorId})`, '🔄');
    } else {
      // Fallback para o formato antigo
      log('🔄', `CRM ATUALIZADO PARA LEAD #${leadId}`, { vendedorId });
    }
  },
  
  // Log de atualização da planilha
  sheetUpdate: (vendedorName: string, leadsRecebidosHoje?: number) => {
    // Formato antigo somente, pois não temos o leadId aqui
    log('🔄', `PLANILHA ATUALIZADA: ${vendedorName}`, leadsRecebidosHoje !== undefined ? { leadsRecebidosHoje } : undefined);
  },
  
  // Log de conclusão de distribuição
  distributionComplete: (leadId: string, vendedorName: string) => {
    if (activeLeadLogs.has(leadId)) {
      // Já adicionamos o vendor selected como último log, só precisamos completar o log
      completeLeadLog(leadId);
    } else {
      // Fallback para o formato antigo
      // Atualizar estatísticas
      dailyStats.distributedLeads++;
      dailyStats.vendedoresCount[vendedorName] = (dailyStats.vendedoresCount[vendedorName] || 0) + 1;
      
      log('📊', `DISTRIBUIÇÃO CONCLUÍDA: Lead #${leadId} => ${vendedorName}`);
    }
  },
  
  // Log de resumo da seleção do vendedor
  selectionSummary: (leadId: string, vendedor: any, motivo: string, criteriosChave: Record<string, any>) => {
    if (activeLeadLogs.has(leadId)) {
      const leadLogger = activeLeadLogs.get(leadId)!;
      
      // Armazenar o motivo para o resumo final
      leadLogger.selectionReason = motivo;
      
      // Criar um resumo mais detalhado para o log
      let resumoDetalhado = `RESUMO DE SELEÇÃO`;
      
      // Adicionar detalhes sobre ocupação do pote se disponível
      if (criteriosChave.ocupacao_pote) {
        resumoDetalhado += ` | Ocupação: ${criteriosChave.ocupacao_pote}`;
      }
      
      // Adicionar leads recebidos/tamanho do pote se disponível
      if (criteriosChave.leads_recebidos !== undefined && criteriosChave.tamanho_pote !== undefined) {
        resumoDetalhado += ` | Pote: ${criteriosChave.leads_recebidos}/${criteriosChave.tamanho_pote}`;
      }
      
      // Adicionar tipo de lead se disponível
      if (criteriosChave.tipo_lead) {
        resumoDetalhado += ` | Tipo Lead: ${criteriosChave.tipo_lead}`;
      }
      
      logForLead(leadId, '├─', resumoDetalhado, '📊');
      logForLead(leadId, '├─', `MOTIVO DA SELEÇÃO: ${motivo}`, '✅', criteriosChave);
    } else {
      // Fallback para o formato antigo
      log('✅', `RESUMO DE SELEÇÃO PARA LEAD #${leadId}`, {
        vendedor_selecionado: vendedor.nomeAbreviado || vendedor.nome,
        motivo_principal: motivo,
        criterios_decisivos: criteriosChave,
        horario_distribuicao: new Date().toLocaleTimeString('pt-BR', { timeZone: 'America/Sao_Paulo' }),
      });
    }
  },
  
  // Log de erro
  error: (message: string, error: any) => {
    // Procurar por leadId na mensagem
    const leadIdMatch = message.match(/Lead #?(\d+)/i);
    let leadId = leadIdMatch ? leadIdMatch[1] : '';
    
    if (leadId && activeLeadLogs.has(leadId)) {
      logForLead(leadId, '├─', `ERRO: ${message}`, '❌', error);
    } else {
      // Fallback para o formato antigo
      log('❌', `ERRO: ${message}`, error);
    }
  },
  
  // Log de aviso
  warning: (message: string, data?: any) => {
    // Procurar por leadId na mensagem
    const leadIdMatch = message.match(/Lead #?(\d+)/i);
    let leadId = leadIdMatch ? leadIdMatch[1] : '';
    
    if (leadId && activeLeadLogs.has(leadId)) {
      logForLead(leadId, '├─', `AVISO: ${message}`, '⚠️', data);
    } else {
      // Fallback para o formato antigo
      log('⚠️', `AVISO: ${message}`, data);
    }
  },
  
  // Log informativo geral
  info: (message: string, data?: any) => {
    // Procurar por leadId na mensagem
    const leadIdMatch = message.match(/Lead #?(\d+)/i);
    let leadId = leadIdMatch ? leadIdMatch[1] : '';
    
    if (leadId && activeLeadLogs.has(leadId)) {
      logForLead(leadId, '├─', message, '📋', data);
    } else {
      // Fallback para o formato antigo
      log('📋', message, data);
    }
  },
  
  // Obter estatísticas em tempo real
  getStats: () => {
    const elapsedTime = Math.round((new Date().getTime() - dailyStats.startTime.getTime()) / 1000);
    const topVendedores = Object.entries(dailyStats.vendedoresCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
      
    return {
      ...dailyStats,
      elapsedTimeSeconds: elapsedTime,
      topVendedores
    };
  }
}; 