import cron from "node-cron";
import { updateSellersFromSheets } from "../utils/sellers.utils";

export const updateSellersCron = () => {
  // Atualização às 07:00
  cron.schedule("0 7 * * *", async () => {
    try {
      console.log("Atualizando vendedores às 07:00...");
      await updateSellersFromSheets();
    } catch (error) {
      console.error("Erro na atualização dos vendedores:", error);
    }
  });
  // Atualização às 20:10
  cron.schedule("10 20 * * *", async () => {
    try {
      console.log("Atualizando vendedores às 20:10...");
      await updateSellersFromSheets();
    } catch (error) {
      console.error("Erro na atualização dos vendedores:", error);
    }
  });
};

