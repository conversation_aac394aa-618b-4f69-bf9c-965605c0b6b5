import axios from "axios";
import { logger } from "./logger.utils";

const discordWebhookUrl =
  "https://discord.com/api/webhooks/1352046514884706385/GSoybzdsGZrpnhwdKRWnK5A6wBUzVOwap3d48euH664tZHotG49dQmInPcGYo5Eny-Zi";

const MAX_DISCORD_MESSAGE_LENGTH = 2000;

export async function sendDiscordMessage(message: string): Promise<void> {
  try {
    // Se a mensagem for menor que o limite, envia diretamente
    if (message.length <= MAX_DISCORD_MESSAGE_LENGTH) {
      await axios.post(discordWebhookUrl, {
        content: message,
      });
      logger.info("Mensagem enviada ao Discord com sucesso!");
      return;
    }

    // Divide a mensagem em partes
    const parts = splitMessage(message);
    
    // Envia cada parte separadamente
    for (let i = 0; i < parts.length; i++) {
      await axios.post(discordWebhookUrl, {
        content: parts[i],
      });
      // Pequeno atraso para evitar rate limiting
      if (i < parts.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }
    
    logger.info(`Mensagem grande enviada ao Discord em ${parts.length} partes com sucesso!`);
  } catch (error) {
    logger.error("Erro ao enviar mensagem ao Discord:", error);
    throw error; // Propaga o erro para tratamento na camada superior
  }
}

/**
 * Divide uma mensagem grande em partes menores respeitando quebras de linha
 */
function splitMessage(message: string): string[] {
  const parts: string[] = [];
  let currentPart = "";
  
  // Divide a mensagem por linhas para tentar manter a formatação
  const lines = message.split("\n");
  
  for (const line of lines) {
    // Se adicionar esta linha ultrapassa o limite
    if (currentPart.length + line.length + 1 > MAX_DISCORD_MESSAGE_LENGTH) {
      // Se a linha atual for muito grande, divide-a em partes menores
      if (line.length > MAX_DISCORD_MESSAGE_LENGTH) {
        // Adiciona a parte atual se não estiver vazia
        if (currentPart.length > 0) {
          parts.push(currentPart);
          currentPart = "";
        }
        
        // Divide a linha grande em partes
        let remainingLine = line;
        while (remainingLine.length > 0) {
          const chunk = remainingLine.substring(0, MAX_DISCORD_MESSAGE_LENGTH);
          parts.push(chunk);
          remainingLine = remainingLine.substring(MAX_DISCORD_MESSAGE_LENGTH);
        }
      } else {
        // Adiciona a parte atual e começa uma nova com a linha atual
        parts.push(currentPart);
        currentPart = line;
      }
    } else {
      // Adiciona a linha à parte atual
      if (currentPart.length > 0) {
        currentPart += "\n" + line;
      } else {
        currentPart = line;
      }
    }
  }
  
  // Adiciona a última parte se não estiver vazia
  if (currentPart.length > 0) {
    parts.push(currentPart);
  }
  
  return parts;
}
