# Endpoint de Redistribuição de Leads

## Visão Geral

O endpoint `/distribuir/redistribuir-lead/:leadId` permite redistribuir um lead que já está atribuído a um vendedor para outro vendedor, seguindo todos os critérios de seleção da distribuição inteligente, mas **excluindo o vendedor atual** da seleção.

## Endpoint

```
POST /distribuir/redistribuir-lead/:leadId
```

### Parâmetros

- `leadId` (path parameter): ID do lead no CRM (PipeRun) que deve ser redistribuído

### Exemplo de Uso

```bash
curl -X POST http://localhost:3000/distribuir/redistribuir-lead/123456
```

## Funcionamento

### 1. Validações Iniciais
- Verifica se o lead existe no banco de dados
- Verifica se o lead está atualmente atribuído a um vendedor
- Busca os dados completos do lead no CRM

### 2. Processamento dos Dados do Lead
- Extrai o Lead Score
- Identifica se é um lead de baixo score (MEI ou score < 70)
- Verifica se é um lead indicado
- Normaliza a origem do lead

### 3. Seleção do Novo Vendedor
- Busca todos os vendedores ativos **EXCLUINDO** o vendedor atual
- Aplica os filtros de distribuição:
  - `filterAndSortVendedores`: Filtra por horário de trabalho e ordena por ocupação do pote
  - `filterVendedoresByInterval`: Aplica filtro de intervalo mínimo entre leads
  - `selectVendor`: Seleciona o vendedor ideal baseado nos critérios:
    - Leads de baixo score
    - Leads indicados
    - Menor proporção de ocupação do pote
    - Vendedores prioritários para tipos específicos

### 4. Execução da Redistribuição
- **Transação no banco de dados:**
  - Decrementa o contador do vendedor atual
  - Atualiza o lead para o novo vendedor
- **Atualização no CRM:**
  - Atualiza o owner_id do lead no PipeRun
- **Atualização de contadores:**
  - Recalcula e atualiza o contador baseado na contagem real de leads
- **Atualização da planilha:**
  - Atualiza a planilha do Google Sheets
- **Notificação:**
  - Envia notificação para o novo vendedor

## Critérios de Seleção

O sistema aplica os mesmos critérios da distribuição normal, mas **exclui o vendedor atual**:

### Filtros Aplicados
1. **Vendedores Ativos**: Apenas vendedores com `ativo = true`
2. **Exclusão do Vendedor Atual**: Remove o vendedor que já possui o lead
3. **Horário de Trabalho**: Prioriza vendedores dentro do horário configurado
4. **Intervalo entre Leads**: Respeita o intervalo mínimo entre leads
5. **Tipo de Lead**: 
   - Leads de baixo score: Prioriza vendedores que aceitam (`leadsLowScore = true`)
   - Leads indicados: Prioriza vendedores que aceitam (`leadsIndicados = true`)

### Critérios de Seleção
1. **Início do Dia**: Se todos os vendedores têm 0 leads, prioriza por maior capacidade
2. **Durante o Dia**: Seleciona vendedor com menor proporção de ocupação do pote
3. **Vendedores Prioritários**: Para leads de baixo score, prioriza IDs específicos
4. **Desempate**: Por tempo desde o último lead recebido

## Respostas

### Sucesso (200)
```json
{
  "message": "Lead redistribuído com sucesso de João para Maria",
  "redistribuicao": {
    "leadId": "123456",
    "vendedorAnterior": {
      "id": "vendor-id-1",
      "nome": "João"
    },
    "novoVendedor": {
      "id": "vendor-id-2", 
      "nome": "Maria"
    }
  }
}
```

### Erros Possíveis

#### 400 - Bad Request
```json
{
  "error": "ID do lead é obrigatório"
}
```

```json
{
  "error": "Lead não está atribuído a nenhum vendedor"
}
```

#### 404 - Not Found
```json
{
  "error": "Lead não encontrado no banco de dados"
}
```

```json
{
  "error": "Lead não encontrado no CRM"
}
```

```json
{
  "message": "Nenhum vendedor disponível para redistribuição"
}
```

```json
{
  "error": "Nenhum vendedor disponível para redistribuição (todos os outros vendedores estão inativos)"
}
```

#### 500 - Internal Server Error
```json
{
  "error": "Erro ao redistribuir o lead"
}
```

## Logs e Monitoramento

O sistema gera logs detalhados durante todo o processo:

- **Início da redistribuição**: Log do lead e vendedor atual
- **Características do lead**: Score, tipo (baixo score/indicado), origem
- **Vendedores disponíveis**: Lista após cada filtro aplicado
- **Seleção do vendedor**: Critérios utilizados e vendedor selecionado
- **Atualizações**: CRM, banco de dados, planilha
- **Notificações**: Sucesso ou falha no envio
- **Conclusão**: Resumo da redistribuição

## Casos de Uso

### 1. Redistribuição por Indisponibilidade
Quando um vendedor fica indisponível e seus leads precisam ser redistribuídos:
```bash
curl -X POST http://localhost:3000/distribuir/redistribuir-lead/123456
```

### 2. Redistribuição por Especialização
Quando um lead precisa ser movido para um vendedor mais especializado:
```bash
curl -X POST http://localhost:3000/distribuir/redistribuir-lead/789012
```

### 3. Redistribuição por Balanceamento
Para equilibrar a carga entre vendedores:
```bash
curl -X POST http://localhost:3000/distribuir/redistribuir-lead/345678
```

## Diferenças da Distribuição Normal

| Aspecto | Distribuição Normal | Redistribuição |
|---------|-------------------|----------------|
| **Vendedores Elegíveis** | Todos os ativos | Todos exceto o atual |
| **Lead no Banco** | Cria novo registro | Atualiza registro existente |
| **Contador Vendedor Atual** | N/A | Decrementa |
| **Contador Novo Vendedor** | Incrementa | Recalcula baseado em contagem real |
| **Notificação** | "NOVO LEAD" | "LEAD REDISTRIBUÍDO" |
| **Logs** | Distribuição inicial | Redistribuição com vendedor anterior |

## Considerações Importantes

1. **Atomicidade**: A redistribuição é executada em transação para garantir consistência
2. **Contagem Real**: Os contadores são sempre recalculados baseados na contagem real de leads
3. **Exclusão Garantida**: O vendedor atual nunca será selecionado novamente
4. **Critérios Mantidos**: Todos os critérios de distribuição inteligente são respeitados
5. **Rastreabilidade**: Logs completos permitem auditoria da redistribuição
