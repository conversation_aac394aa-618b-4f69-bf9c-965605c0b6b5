/**
 * @swagger
 * tags:
 *   name: 
 *   description: |
 *     Esta seção fornece uma referência estática para as tarefas cron configuradas no sistema.
 *     As crons são responsáveis por ações automatizadas que garantem a manutenção e eficiência do LeadHub.
 *
 *     **Tabela de Referência**:
 *
 *     | Nome da Tarefa                | Hor<PERSON>rio            | Descrição                                                                 |
 *     |-------------------------------|--------------------|---------------------------------------------------------------------------|
 *     | Reset Daily Data              | `0 20 * * *`       | Reseta dados diários dos vendedores, como leads recebidos e status.      |
 *     | Update Sellers                | `0 7 * * *`, `10 20 * * *` | Atualiza os vendedores usando a planilha base.                            |
 *     | Redistribute Fallback Leads   | `30 20 * * *`      | <PERSON><PERSON><PERSON><PERSON><PERSON> leads em fallback para vendedores disponíveis.               |
 *
 *     **Legenda do Horário**:
 *     - `0 20 * * *` → Todos os dias às 20:00
 *     - `0 7 * * *` → Todos os dias às 07:00
 *     - `10 20 * * *` → Todos os dias às 20:10
 *     - `30 20 * * *` → Todos os dias às 20:30
 *
 *     **Detalhes Importantes**:
 *     - O formato de horário segue a sintaxe do cron: `minuto hora dia-mês mês dia-semana`.
 *     - Todas as tarefas são executadas no fuso horário configurado no servidor.
 */
