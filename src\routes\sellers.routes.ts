import { Router } from "express";
import { deleteLead<PERSON><PERSON>roller, deleteSeller<PERSON>ontroller, getAllLeadsController, getSellerLeadsController, getSellersController,  getTodaysLeadsController, reassignLead<PERSON>ontroller, registerSellerController, resetCount<PERSON>ontroller, sellerAppLogin, setActiveSellerController, updateBySheetsSellersController, updateSellerController, updateSellerCountController, updateSellersController, updateSellerToken } from "../controllers/sellers.controller";
import { fixVendedoresLeadsCount } from '../scripts/fix-vendedores-count';
import { PrismaClient } from '@prisma/client';

const sellersRouter = Router();

sellersRouter.post("/register-seller", registerSellerController);
sellersRouter.put("/update-seller/:id", updateSellerController);    
//sellersRouter.put("/update-sellers", updateSellersController);    
sellersRouter.put("/update-seller-count/:id", updateSellerCountController);    
sellersRouter.delete("/delete-seller/:id", deleteSellerController);
sellersRouter.get("/get-sellers", getSellersController);
sellersRouter.get("/get-todays-lead/:id", getTodaysLeadsController);
sellersRouter.get("/get-seller-leads/:id", getSellerLeadsController);
sellersRouter.get("/get-all-leads", getAllLeadsController);
sellersRouter.post("/desatribuir-lead", deleteLeadController);
sellersRouter.post("/reatribuir-lead", reassignLeadController);
sellersRouter.post("/reset-count", resetCountController);
sellersRouter.get("/update-sellers-by-sheets", updateBySheetsSellersController);
sellersRouter.post("/:id/update-token", updateSellerToken);
sellersRouter.post("/app-login", sellerAppLogin);
sellersRouter.get("/set-active-seller", setActiveSellerController);

// Rota para corrigir contagem de leads de todos os vendedores
sellersRouter.post("/fix-leads-count", async (req, res) => {
  try {
    await fixVendedoresLeadsCount();
    res.status(200).json({ 
      success: true, 
      message: "Contagem de leads dos vendedores corrigida com sucesso"
    });
  } catch (error) {
    console.error("Erro ao corrigir contagem de leads:", error);
    res.status(500).json({ 
      success: false, 
      message: "Erro ao corrigir contagem de leads",
      error: (error as Error).message
    });
  }
});

export default sellersRouter