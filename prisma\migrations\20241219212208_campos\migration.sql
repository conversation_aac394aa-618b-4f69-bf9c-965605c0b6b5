-- CreateTable
CREATE TABLE "Vendedor" (
    "id" SERIAL NOT NULL,
    "nome" TEXT,
    "nomeAbreviado" TEXT,
    "maxLeadsDia" DOUBLE PRECISION,
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "horarioEntrada" TEXT,
    "horarioSaida" TEXT,
    "leadsExtras" BOOLEAN NOT NULL DEFAULT true,
    "foraDoHorario" BOOLEAN NOT NULL DEFAULT true,
    "ownerId" INTEGER,
    "leadsRecebidosHoje" INTEGER DEFAULT 0,
    "appToken" TEXT,
    "appPass" TEXT,
    "appUser" TEXT,
    "leadsIndicados" BOOLEAN NOT NULL DEFAULT false,
    "recebeuPrimeiroLead" BOOLEAN NOT NULL DEFAULT true,
    "leadsLowScore" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Vendedor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Lead" (
    "id" SERIAL NOT NULL,
    "nome" TEXT,
    "email" TEXT,
    "telefone" TEXT,
    "vendedorId" INTEGER,
    "crmId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "emFallback" BOOLEAN NOT NULL DEFAULT false,
    "tentativasDist" INTEGER NOT NULL DEFAULT 0,
    "score" INTEGER,
    "origem" TEXT,
    "indicado" BOOLEAN NOT NULL DEFAULT false,
    "leadsLowScore" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Lead_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Lead" ADD CONSTRAINT "Lead_vendedorId_fkey" FOREIGN KEY ("vendedorId") REFERENCES "Vendedor"("id") ON DELETE SET NULL ON UPDATE CASCADE;
