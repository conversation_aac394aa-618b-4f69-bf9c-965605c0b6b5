import { PrismaClient } from '@prisma/client';
import * as path from 'path';
import * as fs from 'fs';

const prisma = new PrismaClient();

/**
 * <PERSON>ript para corrigir a contagem de leads recebidos hoje por cada vendedor
 * Este script conta os leads atribuídos nas últimas 24 horas e atualiza o campo leadsRecebidosHoje
 */
async function fixVendedoresLeadsCount() {
  console.log('Iniciando correção de contagem de leads por vendedor...');
  
  try {
    // Definir o intervalo de tempo para 20h de ontem até 20h de hoje
    const timezone = "America/Sao_Paulo";
    const today = new Date();

    // Início do intervalo: 20h do dia anterior
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 1,
      20,
      0,
      0
    );

    // Fim do intervalo: 20h do dia atual
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      20,
      0,
      0
    );

    // Converte para UTC
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: timezone })
    ).toISOString();
    
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: timezone })
    ).toISOString();

    console.log(`Período de contagem: ${startOfDayUtc} a ${endOfDayUtc}`);

    // Buscar todos os vendedores
    const vendedores = await prisma.vendedor.findMany({
      include: {
        leads: {
          where: {
            AND: [
              { createdAt: { gte: startOfDayUtc } },
              { createdAt: { lte: endOfDayUtc } },
            ],
          },
        },
      }
    });

    console.log(`Encontrados ${vendedores.length} vendedores no banco de dados.`);

    // Criar log de correções
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir);
    }
    
    const logFile = path.join(logDir, 'vendedores-count-fix.log');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    fs.appendFileSync(logFile, `\n--- Correção de contagem em ${timestamp} ---\n`);

    // Para cada vendedor, contar leads do período e atualizar leadsRecebidosHoje
    for (const vendedor of vendedores) {
      const leadsCount = vendedor.leads.length;
      const oldCount = vendedor.leadsRecebidosHoje || 0;
      
      // Se a contagem atual for diferente do número real de leads
      if (leadsCount !== oldCount) {
        console.log(`Corrigindo vendedor ${vendedor.nome}: ${oldCount} -> ${leadsCount} leads`);
        
        // Registrar a correção no log
        fs.appendFileSync(logFile, `Vendedor: ${vendedor.nome} (${vendedor.id})\n`);
        fs.appendFileSync(logFile, `  Contagem anterior: ${oldCount}\n`);
        fs.appendFileSync(logFile, `  Contagem correta: ${leadsCount}\n`);
        fs.appendFileSync(logFile, `  Leads no período: ${vendedor.leads.map(l => l.crmId).join(', ')}\n\n`);
        
        // Atualizar a contagem no banco de dados
        await prisma.vendedor.update({
          where: { id: vendedor.id },
          data: { leadsRecebidosHoje: leadsCount }
        });
      } else {
        console.log(`Vendedor ${vendedor.nome}: Contagem correta (${leadsCount} leads)`);
      }
    }

    console.log('Correção concluída com sucesso!');
    fs.appendFileSync(logFile, '--- Correção finalizada ---\n');
    
  } catch (error) {
    console.error('Erro ao corrigir contagem de leads:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script se for chamado diretamente
if (require.main === module) {
  fixVendedoresLeadsCount()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}

export { fixVendedoresLeadsCount }; 