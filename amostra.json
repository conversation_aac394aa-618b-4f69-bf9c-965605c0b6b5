{"success": true, "message": "OK", "data": {"id": ********, "hash": "9kvhuur2l2c915hjhhnzkabk00c14f1", "rdstation_reference": "(37) 99825-1220", "type_reference": null, "reference": null, "temperature": null, "probability": null, "account_id": 13955, "pipeline_id": 56894, "owner_id": 89705, "stage_id": 417800, "person_id": ********, "company_id": null, "lost_reason_id": null, "origin_id": 457297, "started_in_stage_id": 486539, "created_at": "2025-03-29 09:24:27", "title": "<PERSON><PERSON><PERSON>", "description": "09:26 29/03 Integração: (37) 99825-1220", "observation": "09:26 29/03 Integração: (37) 99825-1220", "status": 0, "closed_at": null, "reason_close": null, "deleted": 0, "freezed": 0, "value": 2320, "order": 0, "updated_at": "2025-03-29 16:31:54", "last_stage_updated_at": "2025-03-29 16:31:54", "value_mrr": null, "probably_closed_at": null, "last_contact_at": "2025-03-29 09:26:21", "stage_changed_at": "2025-03-29 14:36:03", "frozen_at": null, "lead_time": 1, "customFields": [{"id": 244296, "name": "mobile_phone", "hash": "022511644db7bc1d8b7b607ab750cedd", "type": 1, "belongs": 1, "value": "(37) 99825-1220", "raw_value": "(37) 99825-1220", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 213657, "name": "Nome da marca ✍️", "hash": "40641d0630e125b85f1d91b2e469b2b4", "type": 1, "belongs": 1, "value": "<PERSON><PERSON><PERSON>", "raw_value": "<PERSON><PERSON><PERSON>", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 627361, "name": "Como nos conheceu", "hash": "44eba4ee59f67419160ecd387d969728", "type": 1, "belongs": 1, "value": "Google", "raw_value": "Google", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 225754, "name": "Nome Lead", "hash": "5e61ab1b7425f4ebae5b0b0aa0745227", "type": 1, "belongs": 1, "value": "Thalles", "raw_value": "Thalles", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 547311, "name": "Motivo do registro", "hash": "f3d22c9aab2dbc4b38e70e11f764921a", "type": 1, "belongs": 1, "value": "Tenho receio de perder a marca", "raw_value": "Tenho receio de perder a marca", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 594785, "name": "Data e hora visita", "hash": "b66315788c90e658c1571c7ca335a6e5", "type": 1, "belongs": 1, "value": "2025-03-29T09:23:03-03:00", "raw_value": "2025-03-29T09:23:03-03:00", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 594786, "name": "Data e hora conversão", "hash": "b226951a1e722b87cb6bdf16e15893ef", "type": 1, "belongs": 1, "value": "2025-03-29T09:26:22-03:00", "raw_value": "2025-03-29T09:26:22-03:00", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 613811, "name": "Dispositivo", "hash": "3cbe466620723d4a0f2174e1d98a9d25", "type": 1, "belongs": 1, "value": "Desktop", "raw_value": "Desktop", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 232453, "name": "Canal", "hash": "09ec28a498ad27023b130d8d1bac0230", "type": 1, "belongs": 1, "value": "/", "raw_value": "/", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 388863, "name": "<PERSON>an<PERSON>", "hash": "cf1ab73f8b0f611734b63f619ef95d73", "type": 1, "belongs": 1, "value": "BFEMP", "raw_value": "BFEMP", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 631782, "name": "UTM Final", "hash": "8cd3283b4af7d496cc84ff1f961207fe", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 480567, "name": "url_conversao", "hash": "1f80ef556fd1adc9efb8a1b4f7d95031", "type": 5, "belongs": 1, "value": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registro%20inpi&device=c&gad_source=1&gbraid=0AAAAAohu6SRxIkzA20CjaE_ukd1_JSnUu&gclid=Cj0KCQjwtJ6_BhDWARIsAGanmKeO4JJpl__xGx0YmdL5G8JhE6NpVZimAlf3WH8cquiHIffoHpb5vhwaAnclEALw_wcB", "raw_value": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registro%20inpi&device=c&gad_source=1&gbraid=0AAAAAohu6SRxIkzA20CjaE_ukd1_JSnUu&gclid=Cj0KCQjwtJ6_BhDWARIsAGanmKeO4JJpl__xGx0YmdL5G8JhE6NpVZimAlf3WH8cquiHIffoHpb5vhwaAnclEALw_wcB", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 631785, "name": "Historico Campanhas", "hash": "c8ac93e1550815982f9030cd58b9d4f7", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 204059, "name": "Lead Score", "hash": "2efae4f52ad645c8fbbd483f586796f8", "type": 1, "belongs": 1, "value": "79", "raw_value": "79", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 196895, "name": "Indicado por", "hash": "ff160b3013b1fb3818a9b250c689b339", "type": 1, "belongs": 1, "value": "Youtube", "raw_value": "Youtube", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 623519, "name": "Consentimento", "hash": "b4c1336997972cb7722fc209e6e508c1", "type": 1, "belongs": 1, "value": "accepted", "raw_value": "accepted", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 229327, "name": " Nome Lead", "hash": "9ddab6c554b26f40c43f8adbc7d0814a", "type": 1, "belongs": 1, "value": "Thalles", "raw_value": "Thalles", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 213595, "name": "Estágio do negócio", "hash": "b581f8cef83aaaa7332129dc2552dd3e", "type": 3, "belongs": 1, "value": "Pretendo começar em breve", "raw_value": "Pretendo começar em breve", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Comecei recentemente", "<PERSON><PERSON> atuo há mais de 6 meses", "<PERSON><PERSON> atuo há mais de 2 anos", "Pretendo começar em breve", "Por enquanto é só um plano"], "selected_options": "Pretendo começar em breve", "values_with_trash": ["Pretendo começar em breve"]}, {"id": 370155, "name": "pipecanal", "hash": "dbdb72e1e1947a63c4fc9f16af7836bf", "type": 1, "belongs": 1, "value": "/", "raw_value": "/", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 521462, "name": "score", "hash": "480ed6abb2be56750429335e98bbcbde", "type": 1, "belongs": 1, "value": "98", "raw_value": "98", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 544091, "name": "fase-do-planejamento", "hash": "383add8ec4fbeb309870653586e667cb", "type": 3, "belongs": 1, "value": "Confirmar se o nome está disponível, antes de criar o logotipo.", "raw_value": "Confirmar se o nome está disponível, antes de criar o logotipo.", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["<PERSON>ão tenho logotipo ainda, mas quero garantir a propriedade do nome.", "Logotipo está pronto e quero registrar antes de iniciar a divulgação.", "Confirmar se o nome está disponível, antes de criar o logotipo.", "Somente saber se está disponível.", "Se estiver disponível, quero registrar.", "Verificar disponibilidade."], "selected_options": "Confirmar se o nome está disponível, antes de criar o logotipo.", "values_with_trash": ["Confirmar se o nome está disponível, antes de criar o logotipo."]}, {"id": 244308, "name": "Quantos colaboradores?", "hash": "b0b3877ee5bde2ebc511f25210f4698c", "type": 3, "belongs": 1, "value": "1 a 2", "raw_value": "1 a 2", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["1 a 2", "3 a 5", "6 a 10", "11 a 50", "+ de 50", "Mais de 50"], "selected_options": "1 a 2", "values_with_trash": ["1 a 2"]}, {"id": 369377, "name": "Possui CNPJ", "hash": "83342876471f8508dd364202916d3b24", "type": 3, "belongs": 1, "value": "Não, mas estamos providenciando.", "raw_value": "Não, mas estamos providenciando.", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["<PERSON>m", "<PERSON><PERSON>, mas já estamos providenciando", "Atuo como pessoa física", "Não, mas estamos providenciando.", "Sim."], "selected_options": "Não, mas estamos providenciando.", "values_with_trash": ["Não, mas estamos providenciando."]}, {"id": 213683, "name": "porte", "hash": "3843ba7f86f1357c881fca480f1d0754", "type": 6, "belongs": 1, "value": ["ME"], "raw_value": "[\"ME\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Pessoa física", "MEI", "ME", "EPP", "<PERSON><PERSON><PERSON>", "Inova Simples", "CNPJ em criação", "Ainda não identificado", "Associação", "<PERSON><PERSON> sei responder.", "DEMAIS"], "selected_options": ["ME"], "values_with_trash": ["ME"]}, {"id": 240584, "name": "Tipo de negócio", "hash": "f91331fcd8a32e775b1c7de2a4632ff5", "type": 3, "belongs": 1, "value": "Outros", "raw_value": "Outros", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Vestu<PERSON><PERSON>", "Fabricação de produtos", "Comércio de produtos de outras marcas", "Prestação de serviço", "Produtor de conteúdo ou produto digital", "Entidade social ou organização religiosa", "Outros", "Comércio de produtos de marca própria"], "selected_options": "Outros", "values_with_trash": ["Outros"]}, {"id": 196382, "name": "Ramo de atuação", "hash": "5e2ab920101b625d8fcc7d657bacae4c", "type": 1, "belongs": 1, "value": "Arquitetura e Urbanismo", "raw_value": "Arquitetura e Urbanismo", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 244306, "name": "sua-marca-possui-site-ou-redes-sociais", "hash": "d411c026484e16660fed999b270549c3", "type": 1, "belongs": 1, "value": "Ainda não", "raw_value": "Ainda não", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 547312, "name": "Grau de conhecimento", "hash": "99903be8bba5e5a2081698321061b451", "type": 1, "belongs": 1, "value": "<PERSON><PERSON><PERSON>", "raw_value": "<PERSON><PERSON><PERSON>", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 554712, "name": "Quanto já investiu na marca", "hash": "c10b17029a905dd53719e773936777a8", "type": 1, "belongs": 1, "value": "Por enquanto nada", "raw_value": "Por enquanto nada", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 554713, "name": "Em quanto tempo quer resolver", "hash": "325820413d0fc4f14d4d1daa84feebcd", "type": 1, "belongs": 1, "value": "Em 1 ou 2 meses", "raw_value": "Em 1 ou 2 meses", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 549882, "name": "IdOriginal", "hash": "fe3516735e9a34b77b65a07a51255344", "type": 14, "belongs": 1, "value": "********", "raw_value": ********, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 594787, "name": "<PERSON><PERSON> entrada lead", "hash": "ccd495150cb952162f0b4342adc3a2a0", "type": 1, "belongs": 1, "value": "29/03/2025, 09:27:16", "raw_value": "29/03/2025, 09:27:16", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 525692, "name": "PTime", "hash": "5f009cc35dc1c8bdd8934a66e206732f", "type": 1, "belongs": 1, "value": "29/03/2025, 09:49:55", "raw_value": "29/03/2025, 09:49:55", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 194463, "name": "Classes ✍️", "hash": "8b7244acfcc87d24fbeebb7286ab6423", "type": 6, "belongs": 1, "value": ["42"], "raw_value": "[\"42\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45"], "selected_options": ["42"], "values_with_trash": ["42"]}, {"id": 194466, "name": "Tipo de marca ✍️", "hash": "9fac515f2eaffe31a354c86baed1cb9a", "type": 6, "belongs": 1, "value": ["Nominativa"], "raw_value": "[\"Nominativa\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Nominativa", "<PERSON><PERSON>", "Figurativa"], "selected_options": ["Nominativa"], "values_with_trash": ["Nominativa"]}, {"id": 203714, "name": "Site e/ou redes sociais", "hash": "8626a4baa93f97d3bbf189ce631b1368", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 225833, "name": "Tipo de serviço", "hash": "f832a9f2ab8eca6ad2cf81cf15050ebb", "type": 6, "belongs": 1, "value": ["Registro de marca"], "raw_value": "[\"Registro de marca\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Registro de marca", "Renovação de registro", "Atualização de registro", "Recurso administrativo", "Manifestação à Oposição", "Contranotificação"], "selected_options": ["Registro de marca"], "values_with_trash": ["Registro de marca"]}, {"id": 225839, "name": "Viabilidade de registro ✍️", "hash": "fb4b71d1a007bcf589278560161ee30d", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Viável ✅", "Inviável 🚫", "Viável com restrições", "Viável em algumas classes", "<PERSON><PERSON><PERSON><PERSON>", "Viável no conjunto misto"], "selected_options": null, "values_with_trash": null}, {"id": 474111, "name": "Estratégia de atendimento e diagnóstico ✍️", "hash": "ef2dfea143f848b044a09538a4a4a2a8", "type": 5, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 228243, "name": "Como foi atendido? ✍️", "hash": "a5236860f6af80fc755fb8be5b159c7d", "type": 6, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Ligação Telefônica", "WhatsApp", "E-mail", "Video conferência", "Presencial", "PDF", "I<PERSON>ress<PERSON>", "Instagram", "Linkedin", "Outra rede social"], "selected_options": null, "values_with_trash": null}, {"id": 213682, "name": "Negociação ✍️", "hash": "8fde08d168df09b1f7b785d15ee90568", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Valor normal", "Valor promocional"], "selected_options": null, "values_with_trash": null}, {"id": 512979, "name": "<PERSON><PERSON><PERSON><PERSON> de Crédito ✍️", "hash": "5e62d130552affa0e8e73b6f8387a554", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Recusado", "Arriscado - Necess<PERSON>rio confirmar pagamento da entrada para que possamos realizar o protocolo.", "<PERSON>ova<PERSON>, pagamento de entrada em até 7 dias, protocolo padrão.", "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.", "Não é necessário realizar a análise de crédito."], "selected_options": null, "values_with_trash": null}, {"id": 213659, "name": "Proposta no Qwril ✍️", "hash": "33ff940ed3cc66bc790db9a913ce08f6", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["<PERSON>m", "Não"], "selected_options": null, "values_with_trash": null}, {"id": 213684, "name": "<PERSON>", "hash": "236fb94f5d58a794692a3415ef35adf0", "type": 9, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 226320, "name": "Especificações", "hash": "b6c2ad9120aaf13d3e8374bd3d43ae8d", "type": 5, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}]}}