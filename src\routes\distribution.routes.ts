import { Router } from "express";
import {
  distributionController,
  getDistributionReportController,
  getDistributionLogDiagnosticController,
  getDistributionReportByDateController,
  getCurrentDistributionController,
  redistributeLeadController
} from "../controllers/distribution.controller";

const distributionRouter = Router();

distributionRouter.post("/distribuir-lead", distributionController);
distributionRouter.post("/redistribuir-lead/:leadId", redistributeLeadController);
distributionRouter.get("/relatorio-diario", getDistributionReportController);
distributionRouter.get("/diagnostico-logs", getDistributionLogDiagnosticController);
distributionRouter.get("/relatorio-por-data", getDistributionReportByDateController);
distributionRouter.get("/status-atual", getCurrentDistributionController);

export default distributionRouter;
