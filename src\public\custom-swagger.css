/* Geral */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f9;
    /* Fundo suave */
    color: #333;
    /* Texto */
}
.swagger-ui .topbar {
    background-color: #30B946;
    /* Cor de fundo da topbar */
    padding: 10px;
}

.swagger-ui .topbar .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.swagger-ui .topbar .logo-container img {
    max-height: 50px;
    /* Ajuste da altura da logo */
    max-width: 150px;
    /* Ajuste da largura da logo */
}

.swagger-ui .topbar .topbar-wrapper {
    display: none;
    /* Esconde o título padrão */
}
/* Headers */
.swagger-ui .opblock.opblock-post {
    border-left: 5px solid #30B946;
    /* Destaque verde para POST */
    background-color: #e8f9f1;
}

.swagger-ui .opblock.opblock-get {
    border-left: 5px solid #2d8b38;
    /* Destaque verde mais escuro para GET */
    background-color: #eef7ee;
}

/* <PERSON><PERSON><PERSON><PERSON> */
.swagger-ui .btn {
    background-color: #30B946 !important;
    border-color: #30B946 !important;
    color: white !important;
    border-radius: 5px !important;
}

.swagger-ui .btn:hover {
    background-color: #2d8b38 !important;
    border-color: #2d8b38 !important;
}

/* Tabelas */
.swagger-ui table {
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 14px;
    min-width: 400px;
}

.swagger-ui table thead tr {
    background-color: #bcbcbc;
    color: #30B946;
    text-align: left;
    padding-left: 2rem;
}

.swagger-ui table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.swagger-ui table tbody tr:hover {
    background-color: #d4f3dc;
}

/* Rodapé */
.swagger-ui .footer {
    background-color: #30B946;
    color: white;
    text-align: center;
    padding: 10px;
    font-size: 12px;
}