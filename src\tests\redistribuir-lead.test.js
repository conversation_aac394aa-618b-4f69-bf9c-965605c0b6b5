/**
 * Exemplo de teste para o endpoint de redistribuição de leads
 *
 * Este arquivo demonstra como testar o endpoint /distribuir/redistribuir-lead/:crmId
 * usando diferentes cenários.
 */

const axios = require('axios');

// Configuração base
const BASE_URL = 'http://localhost:3000';
const DISTRIBUTION_ENDPOINT = `${BASE_URL}/distribuir`;

/**
 * Teste básico de redistribuição
 */
async function testBasicRedistribution() {
  console.log('🧪 Testando redistribuição básica...');

  try {
    const crmId = '123456'; // Substitua por um CRM ID real de lead
    const response = await axios.post(`${DISTRIBUTION_ENDPOINT}/redistribuir-lead/${crmId}`);
    
    console.log('✅ Redistribuição bem-sucedida:');
    console.log('Status:', response.status);
    console.log('Resposta:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ Erro na redistribuição:');
    console.log('Status:', error.response?.status);
    console.log('Erro:', error.response?.data || error.message);
    
    return null;
  }
}

/**
 * Teste com lead inexistente
 */
async function testNonExistentLead() {
  console.log('🧪 Testando lead inexistente...');

  try {
    const crmId = '999999'; // CRM ID que não existe
    const response = await axios.post(`${DISTRIBUTION_ENDPOINT}/redistribuir-lead/${crmId}`);
    
    console.log('⚠️ Resposta inesperada (deveria dar erro 404):');
    console.log('Status:', response.status);
    console.log('Resposta:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Erro 404 esperado para lead inexistente:');
      console.log('Mensagem:', error.response.data.error);
    } else {
      console.log('❌ Erro inesperado:');
      console.log('Status:', error.response?.status);
      console.log('Erro:', error.response?.data || error.message);
    }
  }
}

/**
 * Teste com parâmetro inválido
 */
async function testInvalidParameter() {
  console.log('🧪 Testando parâmetro inválido...');
  
  try {
    const response = await axios.post(`${DISTRIBUTION_ENDPOINT}/redistribuir-lead/`);
    
    console.log('⚠️ Resposta inesperada (deveria dar erro):');
    console.log('Status:', response.status);
    console.log('Resposta:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Erro 404 esperado para rota inválida');
    } else if (error.response?.status === 400) {
      console.log('✅ Erro 400 esperado para parâmetro inválido:');
      console.log('Mensagem:', error.response.data.error);
    } else {
      console.log('❌ Erro inesperado:');
      console.log('Status:', error.response?.status);
      console.log('Erro:', error.response?.data || error.message);
    }
  }
}

/**
 * Teste de múltiplas redistribuições
 */
async function testMultipleRedistributions() {
  console.log('🧪 Testando múltiplas redistribuições...');

  const crmIds = ['123456', '789012', '345678']; // Substitua por CRM IDs reais

  for (const crmId of crmIds) {
    console.log(`\n📋 Redistribuindo lead ${crmId}...`);

    try {
      const response = await axios.post(`${DISTRIBUTION_ENDPOINT}/redistribuir-lead/${crmId}`);
      
      console.log('✅ Sucesso:');
      console.log('Vendedor anterior:', response.data.redistribuicao?.vendedorAnterior?.nome);
      console.log('Novo vendedor:', response.data.redistribuicao?.novoVendedor?.nome);
      
      // Aguardar um pouco entre as redistribuições
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log('❌ Erro:');
      console.log('Status:', error.response?.status);
      console.log('Mensagem:', error.response?.data?.error || error.message);
    }
  }
}

/**
 * Função para verificar o status atual da distribuição
 */
async function checkDistributionStatus() {
  console.log('📊 Verificando status atual da distribuição...');
  
  try {
    const response = await axios.get(`${DISTRIBUTION_ENDPOINT}/status-atual`);
    
    console.log('✅ Status obtido com sucesso:');
    console.log('Total de leads hoje:', response.data.estatisticas.totalLeads);
    console.log('Leads distribuídos:', response.data.estatisticas.leadsDistribuidos);
    console.log('Vendedores ativos:', response.data.estatisticas.vendedoresAtivos);
    
    console.log('\n👥 Top 5 vendedores com mais leads:');
    response.data.vendedores
      .slice(0, 5)
      .forEach((v, index) => {
        console.log(`${index + 1}. ${v.nome}: ${v.leadsHoje}/${v.maxLeadsDia} (${v.ocupacaoPote})`);
      });
    
    return response.data;
  } catch (error) {
    console.log('❌ Erro ao verificar status:');
    console.log('Status:', error.response?.status);
    console.log('Erro:', error.response?.data || error.message);
    
    return null;
  }
}

/**
 * Função principal para executar todos os testes
 */
async function runAllTests() {
  console.log('🚀 Iniciando testes do endpoint de redistribuição...\n');
  
  // Verificar status inicial
  await checkDistributionStatus();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Executar testes
  await testBasicRedistribution();
  console.log('\n' + '-'.repeat(30) + '\n');
  
  await testNonExistentLead();
  console.log('\n' + '-'.repeat(30) + '\n');
  
  await testInvalidParameter();
  console.log('\n' + '-'.repeat(30) + '\n');
  
  await testMultipleRedistributions();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Verificar status final
  await checkDistributionStatus();
  
  console.log('\n✨ Testes concluídos!');
}

/**
 * Função utilitária para testar um lead específico
 */
async function testSpecificLead(crmId) {
  console.log(`🎯 Testando redistribuição do lead ${crmId}...`);

  try {
    const response = await axios.post(`${DISTRIBUTION_ENDPOINT}/redistribuir-lead/${crmId}`);
    
    console.log('✅ Redistribuição bem-sucedida:');
    console.log('Mensagem:', response.data.message);
    
    if (response.data.redistribuicao) {
      const { vendedorAnterior, novoVendedor } = response.data.redistribuicao;
      console.log(`📋 Lead movido de "${vendedorAnterior.nome}" para "${novoVendedor.nome}"`);
    }
    
    return true;
  } catch (error) {
    console.log('❌ Erro na redistribuição:');
    console.log('Status:', error.response?.status);
    console.log('Mensagem:', error.response?.data?.error || error.message);
    
    return false;
  }
}

// Exportar funções para uso em outros arquivos
module.exports = {
  testBasicRedistribution,
  testNonExistentLead,
  testInvalidParameter,
  testMultipleRedistributions,
  checkDistributionStatus,
  runAllTests,
  testSpecificLead
};

// Se executado diretamente, rodar todos os testes
if (require.main === module) {
  runAllTests().catch(console.error);
}

/**
 * INSTRUÇÕES DE USO:
 * 
 * 1. Certifique-se de que o servidor está rodando na porta 3000
 * 2. Substitua os CRM IDs de exemplo por IDs reais de leads do seu sistema
 * 3. Execute o arquivo:
 *    node src/tests/redistribuir-lead.test.js
 *
 * 4. Para testar um lead específico:
 *    const { testSpecificLead } = require('./src/tests/redistribuir-lead.test.js');
 *    testSpecificLead('123456');
 * 
 * 5. Para verificar apenas o status:
 *    const { checkDistributionStatus } = require('./src/tests/redistribuir-lead.test.js');
 *    checkDistributionStatus();
 */
