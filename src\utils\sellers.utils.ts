import { PrismaClient } from "@prisma/client";
import * as config from "../config";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { logger } from "./logger.utils";

const spreadsheetId = "1HRn5UJJnLuiZb6pb9fNwvDAGDR6O3FCZkz5fBz7pE-s";

const prisma = new PrismaClient();

export const getSellersDataBySheets = async (spreadsheetId: string) => {
  const rangeResponse = await config.sheets.spreadsheets.values.get({
    spreadsheetId: spreadsheetId,
    range: "'Lista Distribuição'!A7:AB22", // Atualize o intervalo conforme necessário
  });

  const rows = rangeResponse.data.values;

  if (!rows || rows.length === 0) {
    logger.warning("Nenhum dado encontrado na planilha.");
    return [];
  }

  const sellers = rows.map((row, _index) => {
    let horarioEntrada, horarioSaida;
    horarioEntrada = row[3] || null; // Coluna D
    horarioSaida = row[4] || null; // Coluna E
    return {
      id: row[0], // Coluna A
      nomeAbreviado: row[17], // Coluna R (Nome Abreviado)
      maxLeadsDia: row[6], // Coluna G
      ativo: row[9] === "TRUE", // Coluna J
      leadsExtras: row[7] === "TRUE", // Coluna H
      foraDoHorario: row[8] === "TRUE", // Coluna I
      leadsIndicados: row[10] === "TRUE", // Coluna K
      leadsLowScore: row[11] === "TRUE", // Coluna L
      horarioEntrada,
      horarioSaida,
    };
  });
  return sellers;
};

export async function updateSellersFromSheets() {
  const sellers = await getSellersDataBySheets(spreadsheetId);
  try {
    const today = new Date();

    // Define o início do intervalo: 20h do dia anterior
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 1,
      20,
      0,
      0
    );

    // Define o fim do intervalo: 20h do dia atual
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      20,
      0,
      0
    );

    // Converter para UTC
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const startOfDayIso = startOfDayUtc.toISOString();
    const endOfDayIso = endOfDayUtc.toISOString();

    const updatedSellers = await Promise.all(
      sellers.map(async (seller: any) => {
        const sellerId = seller.id;
        logger.info(`Atualizando vendedor: ${sellerId}`);
        
        // Buscar vendedor com contagem de leads no período correto
        const existingSeller = await prisma.vendedor.findUnique({
          where: { id: sellerId },
          include: {
            leads: {
              where: {
                AND: [
                  { createdAt: { gte: startOfDayIso } },
                  { createdAt: { lte: endOfDayIso } },
                ],
              },
              orderBy: {
                createdAt: 'asc'
              }
            }
          }
        });

        if (!existingSeller) {
          return { id: sellerId, error: "Vendedor não encontrado" };
        }
        
        const maxLeadsDia = parseFloat(seller.maxLeadsDia.replace(",", ".")) || 0.0;
        const currentLeadsCount = existingSeller.leads.length;
        const leadsRecebidosHoje = existingSeller.leadsRecebidosHoje || 0;

        // Verificar inconsistências na contagem
        if (currentLeadsCount !== leadsRecebidosHoje) {
          logger.warning(`⚠️ INCONSISTÊNCIA: Contagem de leads diferente do esperado para ${seller.nomeAbreviado}`, {
            vendedor: seller.nomeAbreviado,
            contadorAtual: leadsRecebidosHoje,
            leadsReais: currentLeadsCount,
            periodo: {
              inicio: startOfDay.toLocaleString("pt-BR"),
              fim: endOfDay.toLocaleString("pt-BR")
            },
            primeiroCriado: existingSeller.leads[0]?.createdAt,
            ultimoCriado: existingSeller.leads[existingSeller.leads.length - 1]?.createdAt
          });
        }

        // Validar mudanças significativas no tamanho do pote
        if (existingSeller.maxLeadsDia && maxLeadsDia < existingSeller.maxLeadsDia && currentLeadsCount > maxLeadsDia) {
          logger.warning(`⚠️ ALERTA: Redução do tamanho do pote para ${seller.nomeAbreviado}`, {
            vendedor: seller.nomeAbreviado,
            poteAntigo: existingSeller.maxLeadsDia,
            poteNovo: maxLeadsDia,
            leadsAtuais: currentLeadsCount,
            status: 'ACIMA_DO_LIMITE',
            horaAtual: new Date().toISOString()
          });
        }

        logger.info(`📊 STATUS ${seller.nomeAbreviado}: Pote ${maxLeadsDia} (anterior: ${existingSeller.maxLeadsDia}), Leads hoje: ${currentLeadsCount}, Contador: ${leadsRecebidosHoje}`);

        // Atualizar o vendedor com os novos dados e corrigir o contador
        const updatedSeller = await prisma.vendedor.update({
          where: { id: sellerId },
          data: {
            maxLeadsDia: maxLeadsDia,
            leadsExtras: seller.leadsExtras,
            foraDoHorario: seller.foraDoHorario,
            ativo: seller.ativo,
            horarioEntrada: seller.horarioEntrada,
            horarioSaida: seller.horarioSaida,
            leadsIndicados: seller.leadsIndicados,
            leadsLowScore: seller.leadsLowScore,
            // Corrigir o contador com o número real de leads
            leadsRecebidosHoje: currentLeadsCount
          },
        });

        return updatedSeller;
      })
    );
    return updatedSellers;
  } catch (error) {
    logger.error("Erro ao atualizar vendedores:", error);
    throw new Error("Erro interno do servidor");
  }
}

dayjs.extend(duration);

export const filterVendedoresByInterval = async (
  allVendedores: any[],
  spreadsheetId: string
) => {
  logger.info("🔍 Iniciando filtragem de vendedores por intervalo entre leads");

  try {
    // Buscar os intervalos da planilha
    const rangeResponse = await config.sheets.spreadsheets.values.get({
      spreadsheetId: spreadsheetId,
      range: "'Lista Distribuição'!AD7:AD22",
    });

    const intervalos = rangeResponse.data.values;

    if (!intervalos || intervalos.length === 0) {
      logger.warning("Nenhum intervalo encontrado na planilha", { 
        acao: "Utilizando todos os vendedores disponíveis" 
      });
      
      // Garantir que todos os vendedores tenham dados atualizados antes de retornar
      const vendedoresAtualizados = await Promise.all(
        allVendedores.map(async (vendedor) => {
          try {
            // Buscar dados atualizados do vendedor
            const vendedorAtualizado = await prisma.vendedor.findUnique({
              where: { id: vendedor.id },
              select: { leadsRecebidosHoje: true, maxLeadsDia: true }
            });
            
            if (vendedorAtualizado) {
              // Certificar que os valores numéricos estão definidos
              if (vendedorAtualizado.leadsRecebidosHoje !== null && vendedorAtualizado.leadsRecebidosHoje !== undefined) {
                vendedor.leadsRecebidosHoje = vendedorAtualizado.leadsRecebidosHoje;
              }
              
              if (vendedorAtualizado.maxLeadsDia !== null && vendedorAtualizado.maxLeadsDia !== undefined && vendedorAtualizado.maxLeadsDia > 0) {
                vendedor.maxLeadsDia = vendedorAtualizado.maxLeadsDia;
              }
            }
            
            return {
              ...vendedor,
              intervaloDuration: dayjs.duration({ minutes: 0 })
            };
          } catch (error) {
            logger.error(`Erro ao atualizar vendedor ${vendedor.id}`, error);
            return {
              ...vendedor,
              intervaloDuration: dayjs.duration({ minutes: 0 })
            };
          }
        })
      );
      
      return vendedoresAtualizados;
    }

    logger.info("Intervalos configurados na planilha", intervalos);

    // Mapear vendedores com seus intervalos
    const vendedoresComIntervalo = await Promise.all(
      allVendedores.map(async (vendedor, index) => {
        // Buscar dados atualizados do vendedor
        try {
          const vendedorAtualizado = await prisma.vendedor.findUnique({
            where: { id: vendedor.id },
            select: { leadsRecebidosHoje: true, maxLeadsDia: true }
          });
          
          if (vendedorAtualizado) {
            // Certificar que os valores numéricos estão definidos
            if (vendedorAtualizado.leadsRecebidosHoje !== null && vendedorAtualizado.leadsRecebidosHoje !== undefined) {
              vendedor.leadsRecebidosHoje = vendedorAtualizado.leadsRecebidosHoje;
            }
            
            if (vendedorAtualizado.maxLeadsDia !== null && vendedorAtualizado.maxLeadsDia !== undefined && vendedorAtualizado.maxLeadsDia > 0) {
              vendedor.maxLeadsDia = vendedorAtualizado.maxLeadsDia;
            }
          }
        } catch (error) {
          logger.error(`Erro ao atualizar vendedor ${vendedor.id}`, error);
        }
        
        const intervaloStr = intervalos[index]?.[0] || "0:00"; // Intervalo ou padrão 0:00
        const [hours, minutes] = intervaloStr.split(":").map(Number);
        const intervaloDuration = dayjs.duration({ hours, minutes });
        const intervaloMinutos = intervaloDuration.asMinutes();

        logger.info(`Intervalo para vendedor ${vendedor.nome}`, { 
          vendedor: vendedor.nome, 
          intervalo_minutos: intervaloMinutos 
        });

        return { ...vendedor, intervaloDuration };
      })
    );

    // Filtrar vendedores
    const vendedoresDisponiveis = await Promise.all(
      vendedoresComIntervalo.map(async (vendedor) => {
        const lastLead = await prisma.lead.findFirst({
          where: { vendedorId: vendedor.id },
          orderBy: { createdAt: "desc" },
        });

        if (!lastLead) {
          logger.intervalCheck(vendedor, true);
          return vendedor;
        }

        const lastLeadTime = dayjs(lastLead.createdAt);
        const now = dayjs();
        const minutesSinceLastLead = now.diff(lastLeadTime, "minute");
        const requiredInterval = vendedor.intervaloDuration.asMinutes();
        const isAvailable = minutesSinceLastLead >= requiredInterval;

        // Usar o método intervalCheck do logger
        logger.intervalCheck(
          vendedor, 
          isAvailable, 
          lastLead.createdAt, 
          requiredInterval
        );

        return isAvailable ? vendedor : null;
      })
    );

    const vendedoresFiltrados = vendedoresDisponiveis.filter(Boolean);

    if (vendedoresFiltrados.length === 0) {
      logger.warning("Nenhum vendedor disponível após verificação de intervalos", { 
        acao: "Incluindo todos os vendedores novamente, mas com dados atualizados" 
      });
      
      // Retornar vendedores originais, mas com intervaloDuration adicionada
      // e dados atualizados
      return vendedoresComIntervalo;
    }

    logger.info("Vendedores disponíveis após filtragem de intervalo", {
      total: vendedoresFiltrados.length,
      vendedores: vendedoresFiltrados.map(v => v.nome)
    });

    return vendedoresFiltrados;
  } catch (error) {
    logger.error("Erro ao acessar planilha do Google Sheets", error);
    
    // Em caso de erro, retorna todos os vendedores com intervalo padrão
    // mas atualiza os dados deles primeiro
    const vendedoresAtualizados = await Promise.all(
      allVendedores.map(async (vendedor) => {
        try {
          // Buscar dados atualizados do vendedor
          const vendedorAtualizado = await prisma.vendedor.findUnique({
            where: { id: vendedor.id },
            select: { leadsRecebidosHoje: true, maxLeadsDia: true }
          });
          
          if (vendedorAtualizado) {
            // Certificar que os valores numéricos estão definidos
            if (vendedorAtualizado.leadsRecebidosHoje !== null && vendedorAtualizado.leadsRecebidosHoje !== undefined) {
              vendedor.leadsRecebidosHoje = vendedorAtualizado.leadsRecebidosHoje;
            }
            
            if (vendedorAtualizado.maxLeadsDia !== null && vendedorAtualizado.maxLeadsDia !== undefined && vendedorAtualizado.maxLeadsDia > 0) {
              vendedor.maxLeadsDia = vendedorAtualizado.maxLeadsDia;
            }
          }
          
          return {
            ...vendedor,
            intervaloDuration: dayjs.duration({ minutes: 0 })
          };
        } catch (error) {
          logger.error(`Erro ao atualizar vendedor ${vendedor.id}`, error);
          return {
            ...vendedor,
            intervaloDuration: dayjs.duration({ minutes: 0 })
          };
        }
      })
    );
    
    return vendedoresAtualizados;
  }
};
