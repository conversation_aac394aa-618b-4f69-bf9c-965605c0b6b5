import { PrismaClient } from "@prisma/client";
import { Request, Response } from "express";
const prisma = new PrismaClient();
import * as config from "../config";
import axios from "axios";
import dotenv from "dotenv";
import { getLeadFromPipeRun, updateGoogleSheet } from "../utils/distribution.utils";
import { logger } from "../utils/logger.utils";
dotenv.config();

const spreadsheetId = "1HRn5UJJnLuiZb6pb9fNwvDAGDR6O3FCZkz5fBz7pE-s";

export const deleteTestLeadsController = async (req: Request, res: Response): Promise<any> => {
  try {
    // Encontre os leads com nome incluindo "TESTE" em maiúsculo
    const testLeads = await prisma.lead.findMany({
      where: { nome: { contains: "TESTE" } }, // Consulta usando Prisma
    });

    // Apague os leads encontrados
    await prisma.lead.deleteMany({
      where: { id: { in: testLeads.map((lead) => lead.id) } }, // Consulta usando Prisma
    });
    res.status(200).json({
      message: `${testLeads.length} leads com o nome incluindo "TESTE" em maiúsculo foram apagados.`,
    });
  } catch (error) {
    logger.error("Erro ao apagar leads", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
};

export const updateLeadController = async (
  req: Request,
  res: Response
): Promise<any> => {
  const crmId = req.params.crmId;
  const leadData = req.body;

  try {
    const leads = await prisma.lead.findMany({
      where: { crmId: crmId },
    });

    const updatedLeads = await Promise.all(
      leads.map(async (lead) => {
        return await prisma.lead.update({
          where: { id: lead.id },
          data: leadData,
        });
      })
    );

    return res.status(200).json(updatedLeads);
  } catch (error) {
    logger.error("Erro ao atualizar leads", error);
    return res.status(500).json({ error: "Erro interno do servidor" });
  }
};
export const getFallbackLeads = async (req: Request, res: Response): Promise<any> => {
  const inFallbackLeads = await prisma.lead.findMany({
    where: { emFallback: true },
  });

  return res.status(200).json(inFallbackLeads);
};
/**
 * Exemplo de requisição:
 * PUT /delegar-lead/NomeVendedor?crmId=123456
 * ou
 * PUT /delegar-lead/NomeVendedor
 * Body: {
 *   "campos_personalizados": {
 *     "ID_CRM": "123456"
 *   }
 * }
 */
export const  delegateLead = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    // Decodificar o nome do vendedor que vem na URL
    const vendedorParam = decodeURIComponent(req.params.vendedorNome);
    let leadCrmId: string;

    // Verifica se o crmId veio por query ou body
    if (req.query.crmId) {
      leadCrmId = req.query.crmId as string;
    } else if (req.body.campos_personalizados?.ID_CRM) {
      leadCrmId = req.body.campos_personalizados.ID_CRM;
    } else {
      return res.status(400).json({ error: "CRM ID não fornecido" });
    }

    logger.info(`Parâmetros recebidos para delegação de lead`, { crmId: leadCrmId, vendedor: vendedorParam });

    // Buscar lead no CRM para verificar owner atual
    const pipeRunLead = await getLeadFromPipeRun(leadCrmId);

    if (!pipeRunLead) {
      logger.warning(`Lead não encontrado no CRM`, { crmId: leadCrmId });
      return res.status(404).json({ error: "Lead não encontrado no CRM" });
    }

    // Buscar vendedor destino baseado no parâmetro recebido
    let novoVendedor;
    const isNumber = !isNaN(Number(vendedorParam));
    logger.info(`Buscando vendedor para delegação`, { parametro: vendedorParam, isNumber });
    
    if (isNumber) {
      novoVendedor = await config.prisma.vendedor.findFirst({
        where: {
          ownerId: Number(vendedorParam)
        },
      });
    } else {
      novoVendedor = await config.prisma.vendedor.findFirst({
        where: {
          OR: [
            { nome: { contains: vendedorParam, mode: "insensitive" } },
            { nomeAbreviado: { contains: vendedorParam, mode: "insensitive" } },
          ],
        },
      });
    }
    
    logger.info(`Vendedor encontrado:`, { vendedor: novoVendedor?.nome, ownerId: novoVendedor?.ownerId });
    
    if (!novoVendedor) {
      logger.warning(`Vendedor não encontrado`, { parametro: vendedorParam });
      return res.status(404).json({ error: "Vendedor destino não encontrado" });
    }

    // Buscar lead no banco interno
    const leadInterno = await config.prisma.lead.findFirst({
      where: { crmId: leadCrmId },
      include: { vendedor: true },
    });

    logger.info(`Lead interno encontrado`, { 
      leadId: leadInterno?.id, 
      vendedorAtual: leadInterno?.vendedor?.nome 
    });

    if (leadInterno && leadInterno.vendedorId === novoVendedor.id) {
      logger.info(`Lead já pertence ao vendedor`, { vendedor: novoVendedor.nome });
      return res.status(200).json({
        message:
          "O lead já pertence ao vendedor tanto no CRM quanto no sistema interno.",
      });
    }

    // Verificar se o owner no CRM é o mesmo do vendedor destino
    if (
      pipeRunLead.owner_id === novoVendedor.ownerId &&
      leadInterno &&
      leadInterno.vendedorId !== novoVendedor.id
    ) {
      logger.info(`Atualizando apenas sistema interno`, { 
        leadId: leadCrmId, 
        novoVendedor: novoVendedor.nome 
      });

      // Realizar transação para atualizar contadores e atribuições
      await config.prisma.$transaction(async (prisma) => {
        if (leadInterno) {
          // Se lead já existe e tem vendedor diferente, decrementar contador do vendedor anterior
          if (
            leadInterno.vendedorId &&
            leadInterno.vendedorId !== novoVendedor!.id
          ) {
            await prisma.vendedor.update({
              where: { id: leadInterno.vendedorId },
              data: { leadsRecebidosHoje: { decrement: 1 } },
            });
          }
          // Atualizar lead no sistema interno
          const leadAtualizado = await prisma.lead.update({
            where: { id: leadInterno.id },
            data: {
              vendedorId: novoVendedor!.id,
              emFallback: false,
            },
          });
          // Incrementar contador do novo vendedor
          await prisma.vendedor.update({
            where: { id: novoVendedor!.id },
            data: { leadsRecebidosHoje: { increment: 1 } },
          });
          return leadAtualizado;
        }
      });
    } else {
      logger.info(`Atualizando CRM e sistema interno - Lead:`, { leadId: leadCrmId, novoVendedor: novoVendedor.nome });

      const apiUrl = `https://api.pipe.run/v1/deals/${leadCrmId}`;
      // Atualizar owner no CRM
      const headers = {
        accept: "application/json",
        "content-type": "application/json",
        token: process.env.CRM_TOKEN,
      };
      const requestData = {
        owner_id: novoVendedor.ownerId,
      };
      try {
        await axios.put(apiUrl, requestData, { headers });
        logger.info(`Lead atualizado no PipeRun - CRM ID:`, { leadId: leadCrmId, novoOwner: novoVendedor.ownerId });
      } catch (error) {
        logger.error(`Erro ao atualizar no PipeRun - CRM ID:`, { leadId: leadCrmId, erro: error });
        throw new Error("Erro ao atualizar lead no PipeRun");
      }

      // Mesmo processo de atualização do sistema interno
      const leadInterno = await config.prisma.lead.findFirst({
        where: { crmId: leadCrmId },
        include: { vendedor: true },
      });

      await config.prisma.$transaction(async (prisma) => {
        if (leadInterno) {
          // Se lead já existe e tem vendedor diferente, decrementar contador do vendedor anterior
          if (
            leadInterno.vendedorId &&
            leadInterno.vendedorId !== novoVendedor.id
          ) {
            await prisma.vendedor.update({
              where: { id: leadInterno.vendedorId },
              data: { leadsRecebidosHoje: { decrement: 1 } },
            });
          }
          // Atualizar lead no sistema interno
          const leadAtualizado = await prisma.lead.update({
            where: { id: leadInterno.id },
            data: {
              vendedorId: novoVendedor.id,
              emFallback: false,
            },
          });
          // Incrementar contador do novo vendedor
          await prisma.vendedor.update({
            where: { id: novoVendedor.id },
            data: { leadsRecebidosHoje: { increment: 1 } },
          });
          return leadAtualizado;
        }
      });
    }
    // Atualizar planilha do Google Sheets
    await updateGoogleSheet(spreadsheetId, {
      nomeAbreviado: novoVendedor.nomeAbreviado,
    });

    logger.info(`Processo finalizado com sucesso - Lead:`, { leadId: leadCrmId, novoVendedor: novoVendedor.nome });

    return res.status(200).json({
      message: "Lead delegado com sucesso",
      novoVendedor: novoVendedor.nome,
    });
  } catch (error) {
    logger.error(`Erro ao delegar lead:`, { erro: error });
    return res.status(500).json({ error: "Erro ao delegar lead" });
  }
};
