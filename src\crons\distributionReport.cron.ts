import cron from 'node-cron';
import { generateDistributionReport } from '../services/distribution.service';
import { logger } from '../utils/logger.utils';
import { sendDiscordMessage } from '../utils/discord.utils';

export const distributionReportCron = () => {
  // Cron expression: "0 5 * * *" = todos os dias às 5h da manhã
  cron.schedule('0 5 * * *', async () => {
    try {
      // Pegar a data de ontem
      const hoje = new Date();
      const ontem = new Date(hoje);
      ontem.setDate(hoje.getDate() - 1);

      // Gerar relatório do período (20h do dia anterior até 20h de ontem)
      const { markdown } = await generateDistributionReport(ontem);

      // Enviar para o Discord
      await sendDiscordMessage(markdown);
      
      logger.info('📊 Relatório de distribuição enviado com sucesso');
    } catch (error) {
      logger.error('❌ Erro ao enviar relatório de distribuição:', error);
    }
  }, {
    scheduled: true,
    timezone: "America/Sao_Paulo" // Importante: usar timezone de Brasília
  });

  logger.info('📅 Job de relatório de distribuição agendado para 5h da manhã');
}; 