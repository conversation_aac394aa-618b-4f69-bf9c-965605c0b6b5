module.exports = {
  apps: [
    {
      name: "leadhub-api", // Nome da aplicação
      script: "src/server.ts", // Arquivo principal
      interpreter: "npx", // Utilizar o ts-node diretamente
      interpreter_args: "ts-node", // Argumentos para execução TypeScript
      watch: ["src"], // Observa mudanças na pasta "src"
      ignore_watch: ["node_modules", "logs"], // Ignora mudanças nesses diretórios
      max_memory_restart: "4G", // Reinicia se usar mais de 4GB de memória
      env: {
        NODE_ENV: "development",
        PORT: 3052, // Porta usada no ambiente de desenvolvimento
        NODE_OPTIONS: "--max-old-space-size=4096", // Ajusta limite de heap para 4GB
      },
      env_production: {
        NODE_ENV: "production",
        PORT: 3052, // Porta usada no ambiente de produção
        NODE_OPTIONS: "--max-old-space-size=4096", // Ajusta limite de heap para 4GB
      },
      max_restarts: 5, // Número máximo de reinicializações
      restart_delay: 5000, // Atraso entre reinicializações
      log_date_format: "YYYY-MM-DD HH:mm:ss", // Formato da data nos logs
      error_file: "./logs/pm2-errors.log", // Caminho para logs de erros
      out_file: "./logs/pm2-out.log", // Caminho para logs de saída
      merge_logs: true, // Combina logs de múltiplas instâncias
    },
  ],
};
