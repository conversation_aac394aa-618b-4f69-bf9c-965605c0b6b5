import fs from 'fs';
import path from 'path';
import { logger, dailyStats } from './logger.utils';

// Pasta para armazenar os relatórios
const REPORTS_DIR = path.join(process.cwd(), 'reports');

// Certificar que a pasta de relatórios existe
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR);
}

/**
 * Cria uma barra de progresso visual para o relatório
 */
function createProgressBar(value: number, max: number, length: number = 20): string {
  const percentage = max === 0 ? 0 : Math.min(100, Math.round((value / max) * 100));
  const filledLength = Math.round((length * percentage) / 100);
  const emptyLength = length - filledLength;
  
  return `[${'█'.repeat(filledLength)}${'-'.repeat(emptyLength)}] ${percentage}%`;
}
/**
 * Processa um bloco de log para extrair informações relevantes
 */
function processLogBlock(block: string): any {
  const result = {
    timestamp: '',
    leadId: '',
    leadTitle: '',
    score: '',
    isIndicado: false,
    isLowScore: false,
    vendedor: '',
    motivoSelecao: '',
    isFallback: false,
    criterios: {} as Record<string, any>
  };

  try {
    // Extrair timestamp do cabeçalho do log
    const headerMatch = block.match(/^\[(.+?)\] \| Lead #(\d+) \| (.+?) \| Score: (.+?) \| Indicado: (.+?) \| LowScore: (.+?)$/m);
    if (headerMatch) {
      result.timestamp = headerMatch[1];
      result.leadId = headerMatch[2];
      result.leadTitle = headerMatch[3];
      result.score = headerMatch[4];
      result.isIndicado = headerMatch[5] === 'SIM';
      result.isLowScore = headerMatch[6] === 'SIM';
    } else {
      // Tentar extrair de forma alternativa caso o formato seja diferente
      const timestampMatch = block.match(/^\[(\d{2}-\d{2}-\d{4}, \d{2}:\d{2}:\d{2})\]/);
      if (timestampMatch) {
        result.timestamp = timestampMatch[1];
      }
      
      const leadIdMatch = block.match(/Lead #(\d+)/);
      if (leadIdMatch) {
        result.leadId = leadIdMatch[1];
      }
      
      const leadTitleMatch = block.match(/Lead #\d+ \| (.+?) \|/);
      if (leadTitleMatch) {
        result.leadTitle = leadTitleMatch[1];
      }
      
      const scoreMatch = block.match(/Score: (.+?) \|/);
      if (scoreMatch) {
        result.score = scoreMatch[1];
      }
      
      result.isIndicado = block.includes('Indicado: SIM');
      result.isLowScore = block.includes('LowScore: SIM');
    }

    // Extrair vendedor selecionado
    const vendorMatch = block.match(/VENDEDOR SELECIONADO: (.+?) ✅/);
    if (vendorMatch) {
      result.vendedor = vendorMatch[1];
    }

    // Extrair motivo da seleção
    const motivoMatch = block.match(/MOTIVO DA SELEÇÃO: (.+?)(?:\r?\n|\r|$)/);
    if (motivoMatch) {
      result.motivoSelecao = motivoMatch[1];
    }

    // Extrair critérios de seleção (JSON)
    // Procura por um objeto JSON que começa após "MOTIVO DA SELEÇÃO"
    const jsonStart = block.indexOf('{', block.indexOf('MOTIVO DA SELEÇÃO'));
    if (jsonStart > -1) {
      // Encontrar o final do JSON
      let jsonEnd = block.indexOf('}', jsonStart);
      let depth = 1;
      let pos = jsonStart + 1;
      
      // Para lidar com objetos JSON aninhados
      while (depth > 0 && pos < block.length) {
        if (block[pos] === '{') depth++;
        else if (block[pos] === '}') depth--;
        pos++;
        if (depth === 0) jsonEnd = pos;
      }
      
      if (jsonEnd > jsonStart) {
        const jsonStr = block.substring(jsonStart, jsonEnd + 1);
        try {
          result.criterios = JSON.parse(jsonStr);
        } catch (e) {
          console.log(`Erro ao fazer parse do JSON: ${jsonStr}`);
        }
      }
    }

    // Verificar se é fallback
    result.isFallback = block.includes('FALLBACK:');
  } catch (error) {
    console.error('Erro ao processar bloco de log:', error);
  }

  return result;
}

/**
 * Gera um relatório diário dos leads processados com base no arquivo de log
 */
export const generateDailyReport = (targetDate?: string): string => {
  const LOG_FILE = path.join(process.cwd(), 'logs', 'distribution.log');
  
  if (!fs.existsSync(LOG_FILE)) {
    logger.error('Arquivo de log não encontrado para gerar o relatório', { path: LOG_FILE });
    return '';
  }
  
  try {
    // Se uma data específica for fornecida, usar ela. Caso contrário, usar a data atual
    let today: Date;
    let todayDate: string;
    
    if (targetDate) {
      // Formato esperado: DD-MM-YYYY
      todayDate = targetDate;
      const [day, month, year] = targetDate.split('-');
      today = new Date(`${year}-${month}-${day}`);
    } else {
      today = new Date();
      const dateFormatter = new Intl.DateTimeFormat('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
      const formattedDate = dateFormatter.format(today);
      todayDate = formattedDate.replace(/\//g, '-');
    }
    
    console.log(`Gerando relatório para a data: ${todayDate}`);
    
    // Ler e processar o arquivo de log
    const logContent = fs.readFileSync(LOG_FILE, 'utf8');
    console.log(`Tamanho do arquivo de log: ${logContent.length} bytes`);
    console.log('Primeiros 500 caracteres do log:', logContent.substring(0, 500));
    
    // Dividir o conteúdo por blocos de log (separados por linhas vazias)
    const contentBlocks = logContent.split(/\n\s*\n/);
    console.log(`Total de blocos de log encontrados: ${contentBlocks.length}`);
    
    // Log dos primeiros blocos para debug
    contentBlocks.slice(0, 3).forEach((block, index) => {
      console.log(`\nBloco ${index + 1}:`);
      console.log(block.substring(0, 200) + '...');
    });
    
    // Estatísticas do dia
    const stats = {
      totalLeads: 0,
      leadsPorVendedor: {} as Record<string, number>,
      lowScoreLeads: 0,
      indicatedLeads: 0,
      fallbackLeads: 0,
      vendedoresAtivos: new Set<string>(),
      inicioOperacao: '',
      fimOperacao: '',
      elapsedTime: 0,
      leads: [] as any[]
    };
    
    // Processar cada bloco de log
    for (const block of contentBlocks) {
      if (!block.trim()) continue;
      
      const logInfo = processLogBlock(block);
      
      // Verificar se temos um timestamp válido
      if (!logInfo.timestamp) {
        console.log('Bloco sem timestamp válido:', block.substring(0, 100) + '...');
        continue;
      }
      
      // Extrair a data do timestamp (formato: DD-MM-YYYY)
      const logDate = logInfo.timestamp.split(',')[0].trim();
      console.log(`Processando lead com data: ${logDate} (data alvo: ${todayDate})`);
      
      // Verificar se o log é da data alvo
      const isTargetDate = logDate === todayDate;
      
      if (isTargetDate) {
        console.log(`Lead da data alvo encontrado: #${logInfo.leadId} - ${logInfo.leadTitle}`);
        
        stats.totalLeads++;
        
        if (logInfo.isLowScore) stats.lowScoreLeads++;
        if (logInfo.isIndicado) stats.indicatedLeads++;
        if (logInfo.isFallback) stats.fallbackLeads++;
        
        if (logInfo.vendedor) {
          stats.leadsPorVendedor[logInfo.vendedor] = (stats.leadsPorVendedor[logInfo.vendedor] || 0) + 1;
          stats.vendedoresAtivos.add(logInfo.vendedor);
        }
        
        stats.leads.push(logInfo);
        
        // Atualizar timestamps
        if (!stats.inicioOperacao || logInfo.timestamp < stats.inicioOperacao) {
          stats.inicioOperacao = logInfo.timestamp;
        }
        if (!stats.fimOperacao || logInfo.timestamp > stats.fimOperacao) {
          stats.fimOperacao = logInfo.timestamp;
        }
      } else {
        console.log(`Lead ignorado por não ser da data alvo: ${logDate} != ${todayDate}`);
      }
    }
    
    console.log(`Estatísticas coletadas: ${stats.totalLeads} leads, ${Object.keys(stats.leadsPorVendedor).length} vendedores`);
    
    // Calcular tempo total de operação
    if (stats.inicioOperacao && stats.fimOperacao) {
      try {
        // Converter timestamps para objetos Date
        const parseTimestamp = (timestamp: string) => {
          // Formato: DD-MM-YYYY, HH:MM:SS
          const [datePart, timePart] = timestamp.split(', ');
          const [day, month, year] = datePart.split('-').map(Number);
          const [hours, minutes, seconds] = timePart.split(':').map(Number);
          
          // Mês em JavaScript é baseado em zero (0-11)
          return new Date(year, month - 1, day, hours, minutes, seconds);
        };
        
        const inicio = parseTimestamp(stats.inicioOperacao);
        const fim = parseTimestamp(stats.fimOperacao);
        
        stats.elapsedTime = Math.round((fim.getTime() - inicio.getTime()) / 1000);
      } catch (e) {
        console.error('Erro ao calcular tempo de operação:', e);
      }
    }
    
    // Obter o formato adequado da data para exibição
    const displayDate = targetDate ? 
      todayDate.split('-').reverse().join('/') :  // Converter DD-MM-YYYY para DD/MM/YYYY
      new Intl.DateTimeFormat('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(today);
    
    // Gerar o conteúdo do relatório em Markdown
    let reportContent = `# Relatório Diário de Distribuição de Leads - ${displayDate}\n\n`;
    
    // Resumo
    reportContent += `## Resumo do Dia\n\n`;
    reportContent += `- **Período de Operação:** ${stats.inicioOperacao || 'N/A'} até ${stats.fimOperacao || 'N/A'}\n`;
    reportContent += `- **Tempo Total de Operação:** ${Math.floor(stats.elapsedTime / 3600)}h ${Math.floor((stats.elapsedTime % 3600) / 60)}m ${stats.elapsedTime % 60}s\n`;
    reportContent += `- **Total de Leads Recebidos:** ${stats.totalLeads}\n`;
    
    // Barras de progresso
    if (stats.totalLeads > 0) {
      const distribuidos = stats.totalLeads - stats.fallbackLeads;
      reportContent += `- **Leads Distribuídos:** ${distribuidos} ${createProgressBar(distribuidos, stats.totalLeads)}\n`;
      reportContent += `- **Leads em Fallback:** ${stats.fallbackLeads} ${createProgressBar(stats.fallbackLeads, stats.totalLeads)}\n`;
      
      reportContent += `- **Tipos de Leads:**\n`;
      reportContent += `  - Leads de Baixo Score: ${stats.lowScoreLeads} ${createProgressBar(stats.lowScoreLeads, stats.totalLeads)}\n`;
      reportContent += `  - Leads Indicados: ${stats.indicatedLeads} ${createProgressBar(stats.indicatedLeads, stats.totalLeads)}\n\n`;
    } else {
      reportContent += `- **Leads Distribuídos:** 0\n`;
      reportContent += `- **Leads em Fallback:** 0\n`;
      reportContent += `- **Tipos de Leads:**\n`;
      reportContent += `  - Leads de Baixo Score: 0\n`;
      reportContent += `  - Leads Indicados: 0\n\n`;
    }
    
    // Distribuição por vendedor
    reportContent += `## Distribuição por Vendedor\n\n`;
    
    if (Object.keys(stats.leadsPorVendedor).length > 0) {
      reportContent += `| Vendedor | Leads Recebidos | Porcentagem | Gráfico |\n`;
      reportContent += `|----------|-----------------|-------------|--------|\n`;
      
      const vendedoresOrdenados = Object.entries(stats.leadsPorVendedor)
        .sort((a, b) => b[1] - a[1]);
      
      vendedoresOrdenados.forEach(([vendedor, count]) => {
        const porcentagem = ((count / stats.totalLeads) * 100).toFixed(1);
        const barGraph = createProgressBar(count, stats.totalLeads, 10);
        reportContent += `| ${vendedor} | ${count} | ${porcentagem}% | ${barGraph} |\n`;
      });
    } else {
      reportContent += `*Nenhum lead foi distribuído para vendedores hoje.*\n\n`;
    }
    
    // Detalhes dos leads
    if (stats.leads.length > 0) {
      reportContent += `## Detalhes dos Leads\n\n`;
      reportContent += `| ID | Título | Score | Tipo | Vendedor | Motivo | Critérios |\n`;
      reportContent += `|----|--------|-------|------|----------|--------|------------|\n`;
      
      stats.leads.forEach(lead => {
        const tipo = lead.isLowScore ? 'Baixo Score' : lead.isIndicado ? 'Indicado' : 'Normal';
        const vendedor = lead.vendedor || 'Fallback';
        const criterios = Object.entries(lead.criterios || {})
          .map(([key, value]) => `${key}: ${value}`)
          .join(', ');
        reportContent += `| ${lead.leadId} | ${lead.leadTitle} | ${lead.score} | ${tipo} | ${vendedor} | ${lead.motivoSelecao || 'N/A'} | ${criterios || 'N/A'} |\n`;
      });
    }
    
    // Salvar o relatório
    const reportFileName = `relatorio-leads-${todayDate}.md`;
    const reportPath = path.join(REPORTS_DIR, reportFileName);
    fs.writeFileSync(reportPath, reportContent);
    
    logger.info(`Relatório diário gerado com sucesso`, { 
      path: reportPath,
      stats: {
        totalLeads: stats.totalLeads,
        vendedores: Object.keys(stats.leadsPorVendedor).length,
        periodo: `${stats.inicioOperacao} até ${stats.fimOperacao}`
      }
    });
    
    return reportPath;
  } catch (error) {
    logger.error('Erro ao gerar relatório diário', error);
    return '';
  }
}; 