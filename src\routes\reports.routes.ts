import express from "express";
import { generateReportController } from "../controllers/reports.controller";

const reportsRouter = express.Router();

/**
 * @swagger
 * /reports/generate:
 *   get:
 *     summary: Gera um relatório diário manualmente
 *     description: Gera um relatório diário com estatísticas de distribuição de leads
 *     responses:
 *       200:
 *         description: Relatório gerado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Relatório gerado com sucesso
 *                 reportPath:
 *                   type: string
 *                   example: /reports/relatorio-leads-18-03-2025.md
 *                 reportContent:
 *                   type: string
 *                   example: # Relatório Diário de Distribuição de Leads...
 *       500:
 *         description: Erro ao gerar relatório
 */
reportsRouter.get("/generate", generateReportController);

export default reportsRouter; 