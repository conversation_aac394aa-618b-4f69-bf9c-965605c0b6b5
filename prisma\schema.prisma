generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Vendedor {
  id                  String   @id @default(uuid())
  nome                String?
  nomeAbreviado       String?
  maxLeadsDia         Float?
  ativo               Boolean @default(true)
  horarioEntrada      String?
  horarioSaida        String?
  leadsExtras         Boolean @default(true)
  foraDoHorario       Boolean @default(true)
  ownerId             Int?
  leadsRecebidosHoje  Int?    @default(0)
  appToken            String?
  appPass             String?
  appUser             String?
  leadsIndicados      Boolean @default(false)
  recebeuPrimeiroLead Boolean @default(true)
  leadsLowScore       Boolean @default(false)
  leads               Lead[]
}

model Lead {
  id             String    @id @default(uuid())
  nome           String?
  email          String?
  telefone       String?
  vendedorId     String?
  crmId          String?
  createdAt      DateTime  @default(now())
  emFallback     Boolean   @default(false)
  tentativasDist Int       @default(0)
  score          Int?
  origem         String?
  indicado       Boolean   @default(false)
  leadsLowScore  Boolean   @default(false)
  vendedor       Vendedor? @relation(fields: [vendedorId], references: [id])
}
