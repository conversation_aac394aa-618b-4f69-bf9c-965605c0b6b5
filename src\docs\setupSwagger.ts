import swaggerJsDoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
import { Express } from "express";

export const setupSwagger = (app: Express) => {
  const options = {
    definition: {
      openapi: "3.0.0",
      info: {
        title: "LeadHub API (Distribuição de leads)",
        version: "1.0.0",
        description: "Documentação da API de distribuição de leads",
      },
      tags: [
        { name: "Sellers", description: "Gerenciamento de vendedores" },
        { name: "Leads", description: "Gerenciamento de leads" }, 
        {
          name: "Distribuir", 
          description: "Lógica de distribuição de leads",
        },  
      ],
      servers: [
        {
          url: "https://api.leadhub.registrese.app.br", // Base URL para produção
          description: "Ambiente de produção",
        },
        {
          url: "http://localhost:3039", // Base URL para desenvolvimento
          description: "Ambiente de desenvolvimento",
        },
      ],
    },
    apis: ["./src/docs/**/*.ts"], // Certifique-se de ajustar conforme o caminho real
  };

  const swaggerSpec = swaggerJsDoc(options);
app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, {
    customCssUrl: "/public/custom-swagger.css", // Caminho correto
    customSiteTitle: "LeadHub API",
  })
);};