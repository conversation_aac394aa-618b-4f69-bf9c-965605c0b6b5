import express from "express";
import sellersRouter from "./routes/sellers.routes";
import distributionRouter from "./routes/distribution.routes";
import leadsRouter from "./routes/leads.routes";
import reportsRouter from "./routes/reports.routes";
import cors from "cors"
import { initializeCrons } from "./crons";
import { setupSwagger } from "./docs/setupSwagger";
import path from "path";
import { logger } from './utils/logger.utils';

const app = express();

app.use("/public", express.static(path.join(__dirname, "public")));


app.use(cors())
// Middleware para parsing de JSON
app.use(express.json());

// Inicializar tarefas cron
initializeCrons();

app.use("/sellers", sellersRouter);
app.use("/distribuir", distributionRouter);
app.use("/leads", leadsRouter);
app.use("/reports", reportsRouter);

setupSwagger(app)

// Garantir que os jobs são parados corretamente quando a aplicação for encerrada
process.on('SIGTERM', () => {
  logger.info('🛑 Recebido sinal SIGTERM, encerrando aplicação...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('🛑 Recebido sinal SIGINT, encerrando aplicação...');
  process.exit(0);
});

export default app;
