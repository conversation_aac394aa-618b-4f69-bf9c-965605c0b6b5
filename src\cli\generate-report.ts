#!/usr/bin/env node

import { generateDailyReport } from "../utils/report.utils";
import fs from 'fs';
import path from 'path';

console.log('Iniciando geração de relatório diário...');

try {
  const reportPath = generateDailyReport();
  
  if (reportPath && fs.existsSync(reportPath)) {
    const reportContent = fs.readFileSync(reportPath, 'utf8');
    
    console.log(`\n✅ Relatório gerado com sucesso!\n`);
    console.log(`📂 Caminho: ${reportPath}`);
    console.log(`📏 Tamanho: ${reportContent.length} caracteres`);
    console.log(`\n================================\n`);
    console.log(reportContent);
    console.log(`\n================================\n`);
    console.log('Você pode copiar este relatório e enviar no Discord.');
    
    process.exit(0);
  } else {
    console.error(`\n❌ Falha ao gerar relatório. Nenhum arquivo criado.`);
    process.exit(1);
  }
} catch (error) {
  console.error(`\n❌ Erro ao gerar relatório:`, error);
  process.exit(1);
} 