/**
 * @swagger
 * /leads/deletar-leads-teste:
 *   delete:
 *     summary: <PERSON>paga todos os leads de teste do banco de dados.
 *     tags:
 *       - Leads
 *     description: |
 *       Este endpoint encontra e remove todos os leads no banco de dados cujo nome contém a palavra "TESTE" em maiúsculo.
 *       É utilizado principalmente para limpeza de dados não produtivos.
 *
 *       **Processo:**
 *       - <PERSON><PERSON> todos os leads no banco de dados com `nome` contendo "TESTE".
 *       - Remove os leads encontrados utilizando uma única operação em lote.
 *
 *       **Uso típico:**
 *       - Realizado para limpar dados de teste após execuções de desenvolvimento ou QA.
 *
 *     responses:
 *       200:
 *         description: Leads de teste apagados com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Mensagem indicando quantos leads foram apagados.
 *                   example: "15 leads com o nome incluindo 'TESTE' em maiúsculo foram apagados."
 *       500:
 *         description: Erro interno ao tentar apagar os leads.
 */

/**
 * @swagger
 * /leads/atualizar-lead/{crmId}:
 *   put:
 *     summary: Atualiza as informações de leads com base no ID do CRM.
 *     tags:
 *       - Leads
 *     description: |
 *       Este endpoint permite atualizar os dados de um ou mais leads associados a um determinado `crmId`.
 *       Todos os leads correspondentes no banco de dados serão atualizados com as informações fornecidas no corpo da requisição.
 *
 *       **Campos que podem ser atualizados**:
 *       - `nome` (string): Nome do lead.
 *       - `email` (string): E-mail do lead.
 *       - `telefone` (string): Telefone do lead.
 *       - `vendedorId` (integer): ID do vendedor associado ao lead.
 *       - `emFallback` (boolean): Indica se o lead está em fallback.
 *       - `tentativasDist` (integer): Número de tentativas de distribuição.
 *       - `score` (integer): Score do lead.
 *       - `origem` (string): Origem do lead.
 *       - `indicado` (boolean): Indica se o lead foi indicado.
 *       - `leadsLowScore` (boolean): Indica se o lead tem baixa pontuação.
 *
 *       **Nota:**
 *       Se nenhum lead correspondente ao `crmId` for encontrado, nenhuma atualização será realizada.
 *
 *     parameters:
 *       - in: path
 *         name: crmId
 *         required: true
 *         description: ID do CRM associado aos leads que serão atualizados.
 *         schema:
 *           type: string
 *           example: "crm-123456"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nome:
 *                 type: string
 *                 description: Nome do lead.
 *                 example: "Empresa XYZ"
 *               email:
 *                 type: string
 *                 description: E-mail do lead.
 *                 example: "<EMAIL>"
 *               telefone:
 *                 type: string
 *                 description: Telefone do lead.
 *                 example: "(11) 91234-5678"
 *               vendedorId:
 *                 type: integer
 *                 description: ID do vendedor associado ao lead.
 *                 example: 123
 *               emFallback:
 *                 type: boolean
 *                 description: Indica se o lead está em fallback.
 *                 example: true
 *               tentativasDist:
 *                 type: integer
 *                 description: Número de tentativas de distribuição.
 *                 example: 2
 *               score:
 *                 type: integer
 *                 description: Score do lead.
 *                 example: 85
 *               origem:
 *                 type: string
 *                 description: Origem do lead.
 *                 example: "Campanha de marketing"
 *               indicado:
 *                 type: boolean
 *                 description: Indica se o lead foi indicado.
 *                 example: false
 *               leadsLowScore:
 *                 type: boolean
 *                 description: Indica se o lead tem baixa pontuação.
 *                 example: false
 *     responses:
 *       200:
 *         description: Leads atualizados com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: ID do lead.
 *                     example: "lead-123"
 *                   crmId:
 *                     type: string
 *                     description: ID do CRM associado ao lead.
 *                     example: "crm-123456"
 *                   score:
 *                     type: integer
 *                     description: Novo score do lead.
 *                     example: 85
 *       500:
 *         description: Erro interno ao atualizar os leads.
 */

/**
 * @swagger
 * /leads/buscarLeadsEmFallback:
 *   get:
 *     summary: Busca todos os leads marcados como fallback.
 *     tags:
 *       - Leads
 *     description: |
 *       Este endpoint retorna todos os leads que estão atualmente em estado de fallback, ou seja, leads que não foram atribuídos a nenhum vendedor.
 *       Leads em fallback são frequentemente aqueles que não puderam ser distribuídos devido a restrições de disponibilidade ou regras de negócio.
 *
 *       **Campos retornados**:
 *       - `id` (string): ID do lead.
 *       - `nome` (string): Nome do lead.
 *       - `crmId` (string): ID do CRM associado ao lead.
 *       - `emFallback` (boolean): Indica se o lead está em fallback.
 *       - `createdAt` (datetime): Data e hora de criação do lead.
 *
 *     responses:
 *       200:
 *         description: Lista de leads em fallback retornada com sucesso.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: ID do lead.
 *                     example: "lead-123"
 *                   nome:
 *                     type: string
 *                     description: Nome do lead.
 *                     example: "Empresa XYZ"
 *                   crmId:
 *                     type: string
 *                     description: ID do CRM associado ao lead.
 *                     example: "crm-123456"
 *                   emFallback:
 *                     type: boolean
 *                     description: Indica se o lead está em fallback.
 *                     example: true
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     description: Data e hora de criação do lead.
 *                     example: "2024-12-30T14:45:00Z"
 *       500:
 *         description: Erro interno ao buscar os leads em fallback.
 */
