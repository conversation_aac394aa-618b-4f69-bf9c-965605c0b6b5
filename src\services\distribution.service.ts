import { PrismaClient, Lead } from "@prisma/client";
import { logger } from "../utils/logger.utils";
import { CRMService } from "./crm.service";

const prisma = new PrismaClient();

export const createLead = async (
  crmId: string,
  nome: string,
  selectedVendorId?: string,
  emFallback: boolean = false, // Novo parâmetro para indicar se o lead é em fallback
  score: number = 0,
  origem: string = "",
  indicado: boolean = false,
  isLowScoreLead: boolean = false
) => {
  try {
    // Verificar se o lead já existe no banco pelo crmId
    const existingLead = await prisma.lead.findFirst({
      where: { crmId: crmId },
    });

    if (existingLead) {
      console.warn(`Lead com crmId ${crmId} já existe. Evitando duplicação.`);
      return existingLead; // Retorna o lead existente ao invés de criar um novo
    }

    // Criar o novo lead, já que não existe duplicata
    const lead = await prisma.lead.create({
      data: {
        crmId: crmId,
        nome: nome,
        vendedor: selectedVendorId
          ? { connect: { id: selectedVendorId } }
          : undefined,
        emFallback: emFallback,
        score: score,
        origem: origem,
        indicado: indicado,
        leadsLowScore: isLowScoreLead,
      },
    });

    return lead;
  } catch (error) {
    console.error("Erro ao criar lead:", error);
    throw new Error("Erro ao criar lead");
  }
};
export const updateVendedorLeadsRecebidos = async (
  selectedVendorId: string,
  increment: boolean = true
) => {
  try {
    const data = increment
      ? { leadsRecebidosHoje: { increment: 1 } }
      : { leadsRecebidosHoje: { decrement: 1 } };

    const vendedorSelecionado = await prisma.vendedor.update({
      where: {
        id: selectedVendorId,
      },
      data: data,
    });
    return vendedorSelecionado;
  } catch (error) {
    console.error("Erro ao atualizar leadsRecebidosHoje do vendedor:", error);
    throw new Error("Erro ao atualizar leadsRecebidosHoje do vendedor");
  }
};
export const updateVendedorLeadsRecebidosTest = async (
  selectedVendorId: string
) => {
  try {

    const vendedorSelecionado = await prisma.vendedor.findUnique({
      where: {
        id: selectedVendorId,
      }
    });
    return vendedorSelecionado;
  } catch (error) {
    console.error("Erro ao atualizar leadsRecebidosHoje do vendedor:", error);
    throw new Error("Erro ao atualizar leadsRecebidosHoje do vendedor");
  }
};

interface LeadGroupInfo {
  porte: string[];
  estagioNegocio: string;
}

interface VendedorGrupos {
  porPorte: Record<string, number>;
  porEstagio: Record<string, number>;
}

interface LeadGroups {
  porPorte: Record<string, number>;
  porEstagio: Record<string, number>;
  porVendedor: Record<string, VendedorGrupos>;
}

async function getLeadGroupInfo(lead: Lead): Promise<LeadGroupInfo | null> {
  if (!lead.crmId) return null;

  const dealData = await CRMService.getDealById(lead.crmId);
  if (!dealData) return null;

  const porte = CRMService.getCustomFieldValue(dealData, CRMService.CUSTOM_FIELDS.PORTE) as string[] || [];
  const estagioNegocio = CRMService.getCustomFieldValue(dealData, CRMService.CUSTOM_FIELDS.ESTAGIO_NEGOCIO) as string || 'Não informado';

  return {
    porte,
    estagioNegocio
  };
}

async function generateLeadGroups(leads: Lead[]): Promise<LeadGroups> {
  const groups: LeadGroups = {
    porPorte: {},
    porEstagio: {},
    porVendedor: {}
  };

  // Processar leads em paralelo para melhor performance
  const leadInfoPromises = leads.map(lead => getLeadGroupInfo(lead));
  const leadInfos = await Promise.all(leadInfoPromises);

  leads.forEach((lead, index) => {
    const info = leadInfos[index];
    if (!info || !lead.vendedorId) return;

    // Inicializar estrutura para o vendedor se não existir
    if (!groups.porVendedor[lead.vendedorId]) {
      groups.porVendedor[lead.vendedorId] = {
        porPorte: {},
        porEstagio: {}
      };
    }

    // Agrupar por porte
    info.porte.forEach(p => {
      groups.porPorte[p] = (groups.porPorte[p] || 0) + 1;
      if (lead.vendedorId) {
        groups.porVendedor[lead.vendedorId].porPorte[p] = (groups.porVendedor[lead.vendedorId].porPorte[p] || 0) + 1;
      }
    });

    // Agrupar por estágio
    groups.porEstagio[info.estagioNegocio] = (groups.porEstagio[info.estagioNegocio] || 0) + 1;
    if (lead.vendedorId) {
      groups.porVendedor[lead.vendedorId].porEstagio[info.estagioNegocio] = 
        (groups.porVendedor[lead.vendedorId].porEstagio[info.estagioNegocio] || 0) + 1;
    }
  });

  return groups;
}

/**
 * Gera um relatório de distribuição de leads para uma data específica
 * @param data Data para o relatório (início 20h do dia anterior, fim 20h da data)
 * @param periodoCompleto Se true, usa o dia completo (00:00 às 23:59) em vez do padrão (20h às 20h)
 */
export async function generateDistributionReport(data: Date, periodoCompleto: boolean = false) {
  try {
    const timezone = "America/Sao_Paulo";
    
    let startOfDay: Date;
    let endOfDay: Date;
    
    if (periodoCompleto) {
      // Define o início do intervalo: 00:00 do dia
      startOfDay = new Date(
        data.getFullYear(),
        data.getMonth(),
        data.getDate(),
        0, 0, 0
      );

      // Define o fim do intervalo: 23:59 do dia
      endOfDay = new Date(
        data.getFullYear(),
        data.getMonth(),
        data.getDate(),
        23, 59, 59
      );
      
      logger.info(`Usando período completo do dia: ${startOfDay.toLocaleString()} até ${endOfDay.toLocaleString()}`);
    } else {
      // Define o início do intervalo: 20h do dia anterior
      startOfDay = new Date(
        data.getFullYear(),
        data.getMonth(),
        data.getDate() - 1,
        20, 0, 0
      );

      // Define o fim do intervalo: 20h do dia atual
      endOfDay = new Date(
        data.getFullYear(),
        data.getMonth(),
        data.getDate(),
        20, 0, 0
      );
      
      logger.info(`Usando período padrão: ${startOfDay.toLocaleString()} até ${endOfDay.toLocaleString()}`);
    }

    // Converter para UTC
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const startOfDayIso = startOfDayUtc.toISOString();
    const endOfDayIso = endOfDayUtc.toISOString();

    // Logs de diagnóstico para verificar o intervalo de datas
    logger.info(`Relatório: Data solicitada: ${data.toISOString()}`);
    logger.info(`Relatório: Intervalo de busca local: ${startOfDay.toLocaleString()} até ${endOfDay.toLocaleString()}`);
    logger.info(`Relatório: Intervalo de busca UTC: ${startOfDayIso} até ${endOfDayIso}`);
    
    // Verificar todos os leads no período (para diagnóstico)
    const todosLeadsNoPeriodo = await prisma.lead.findMany({
      where: {
        AND: [
          { createdAt: { gte: startOfDayIso } },
          { createdAt: { lte: endOfDayIso } },
        ],
      },
    });
    logger.info(`Relatório: Total de leads encontrados no período (incluindo fallback): ${todosLeadsNoPeriodo.length}`);
    
    // Buscar todos os vendedores e seus leads no período
    const vendedores = await prisma.vendedor.findMany({
      include: {
        leads: {
          where: {
            AND: [
              { createdAt: { gte: startOfDayIso } },
              { createdAt: { lte: endOfDayIso } },
              { emFallback: false } // Excluir leads em fallback
            ],
          },
          orderBy: { createdAt: 'asc' }
        },
      },
      orderBy: { nome: 'asc' }
    });
    
    logger.info(`Relatório: Total de vendedores: ${vendedores.length}`);
    
    // Analisar detalhes dos leads para debugging
    const todosLeads = todosLeadsNoPeriodo;
    // Analisar estrutura dos dados de alguns leads
    if (todosLeads.length > 0) {
      const amostraLead = todosLeads[0];
      logger.info(`Relatório: Análise de exemplo de lead ID ${amostraLead.id}`, {
        campos_disponiveis: Object.keys(amostraLead),
        score: amostraLead.score,
        criado_em: amostraLead.createdAt,
        vendor_id: amostraLead.vendedorId,
        tipo: {
          lowscore: amostraLead.leadsLowScore,
          indicado: amostraLead.indicado
        }
      });
    }
    
    // Calcular médias de score
    let totalScore = 0;
    let totalLeadsComScore = 0;
    
    // Logs detalhados para diagnóstico do problema de contagem
    let totalLeadsVendedores = 0;
    const scoresPorVendedor: Record<string, {total: number, count: number}> = {};
    
    vendedores.forEach(v => {
      totalLeadsVendedores += v.leads.length;
      const vendedorId = v.id;
      scoresPorVendedor[vendedorId] = { total: 0, count: 0 };
      
      logger.info(`Relatório: Vendedor ${v.nomeAbreviado || v.nome}: ${v.leads.length} leads`);
      
      // Logar cada lead individualmente para depuração
      if (v.leads.length > 0) {
        v.leads.forEach((lead, index) => {
          logger.info(`Relatório: Vendedor ${v.nomeAbreviado || v.nome} - Lead ${index+1}: ID ${lead.id}, CRM ID ${lead.crmId}, Score ${lead.score}, Data ${lead.createdAt}`);
          
          // Acumular scores para médias
          if (lead.score !== null && lead.score !== undefined) {
            totalScore += Number(lead.score);
            totalLeadsComScore++;
            
            scoresPorVendedor[vendedorId].total += Number(lead.score);
            scoresPorVendedor[vendedorId].count++;
          }
        });
        
        logger.info(`Relatório: Primeiro lead: ${v.leads[0].createdAt}, Último lead: ${v.leads[v.leads.length - 1].createdAt}`);
      }
    });
    
    // Calcular a média geral de score
    const mediaScoreGeral = totalLeadsComScore > 0 ? (totalScore / totalLeadsComScore) : 0;
    logger.info(`Relatório: Média de score geral: ${mediaScoreGeral.toFixed(1)} (baseado em ${totalLeadsComScore} leads)`);
    
    logger.info(`Relatório: Total de leads atribuídos a vendedores: ${totalLeadsVendedores} (de ${todosLeadsNoPeriodo.length} encontrados)`);
    
    // Detalhar os leads não associados a vendedores
    const leadsNaoAssociados = todosLeadsNoPeriodo.filter(lead => {
      // Verificar se este lead está em algum dos vendedores
      return !vendedores.some(v => 
        v.leads.some(vLead => vLead.id === lead.id)
      );
    });
    
    logger.info(`Relatório: Leads não associados a vendedores: ${leadsNaoAssociados.length}`);
    
    // Logar detalhes de alguns destes leads para debug
    leadsNaoAssociados.slice(0, 5).forEach((lead, index) => {
      logger.info(`Relatório: Lead não associado ${index+1}: ID ${lead.id}, CRM ID ${lead.crmId}, Score ${lead.score}, Data ${lead.createdAt}, VendedorId: ${lead.vendedorId || 'null'}, Fallback: ${lead.emFallback}`);
    });
    
    // Buscar leads em fallback
    const leadsEmFallback = await prisma.lead.findMany({
      where: {
        AND: [
          { createdAt: { gte: startOfDayIso } },
          { createdAt: { lte: endOfDayIso } },
          { emFallback: true }
        ],
      },
    });
    logger.info(`Relatório: Leads em fallback: ${leadsEmFallback.length}`);
    
    // Calcular totais
    const totalLeads = totalLeadsVendedores + leadsEmFallback.length;
    const totalLeadsDistribuidos = totalLeads - leadsEmFallback.length;
    const vendedoresComLeads = vendedores.filter(v => v.leads.length > 0).length;
    const mediaLeadsPorVendedor = vendedoresComLeads > 0 ? 
      (totalLeadsDistribuidos / vendedoresComLeads).toFixed(1) : "0";
    
    // Calcular leads por tipo
    const leadsLowScore = vendedores.reduce((sum, v) => 
      sum + v.leads.filter(l => l.leadsLowScore).length, 0);
    const leadsIndicados = vendedores.reduce((sum, v) => 
      sum + v.leads.filter(l => l.indicado).length, 0);
    
    // Buscar informações de grupos para todos os leads
    const todosLeadsParaGrupos = todosLeadsNoPeriodo.filter(lead => !lead.emFallback);
    const gruposDeLeads = await generateLeadGroups(todosLeadsParaGrupos);
    
    // Formatar data para título do relatório
    const dataFormatada = data.toLocaleDateString('pt-BR', {
      timeZone: timezone,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    
    // Formatar período do relatório
    const periodoInicio = startOfDay.toLocaleString('pt-BR', {
      timeZone: timezone,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    
    const periodoFim = endOfDay.toLocaleString('pt-BR', {
      timeZone: timezone,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    
    // Log do resumo
    logger.info(`Relatório: Resumo - Total: ${totalLeads}, Distribuídos: ${totalLeadsDistribuidos}, Fallback: ${leadsEmFallback.length}`);
    
    // Estrutura de dados para o relatório
    const relatorio = {
      data: dataFormatada,
      periodo: {
        inicio: periodoInicio,
        fim: periodoFim,
        tipo: periodoCompleto ? 'completo' : 'padrão'
      },
      resumo: {
        totalLeads,
        leadsDistribuidos: totalLeadsDistribuidos,
        leadsEmFallback: leadsEmFallback.length,
        leadsLowScore,
        leadsIndicados,
        vendedoresTotais: vendedores.length,
        vendedoresComLeads,
        mediaLeadsPorVendedor,
        mediaScore: mediaScoreGeral.toFixed(1),
        leadsComScore: totalLeadsComScore
      },
      grupos: {
        porPorte: Object.entries(gruposDeLeads.porPorte)
          .sort(([, a], [, b]) => b - a)
          .map(([porte, quantidade]) => ({ porte, quantidade })),
        porEstagio: Object.entries(gruposDeLeads.porEstagio)
          .sort(([, a], [, b]) => b - a)
          .map(([estagio, quantidade]) => ({ estagio, quantidade })),
        porVendedor: gruposDeLeads.porVendedor
      },
      vendedores: vendedores
        .filter(v => v.leads.length > 0)
        .sort((a, b) => b.leads.length - a.leads.length)
        .map(v => {
          const maxLeadsDia = typeof v.maxLeadsDia === 'number' ? v.maxLeadsDia : 0;
          const ocupacao = maxLeadsDia > 0 ? 
            Math.round((v.leads.length / maxLeadsDia) * 100) : 0;
          
          const percentualTotal = totalLeadsDistribuidos > 0 ?
            Math.round((v.leads.length / totalLeadsDistribuidos) * 100) : 0;
          
          // Calcular média de score para este vendedor
          const vendedorScore = scoresPorVendedor[v.id] || { total: 0, count: 0 };
          const mediaScoreVendedor = vendedorScore.count > 0 ? 
            (vendedorScore.total / vendedorScore.count).toFixed(1) : "N/A";

          // Obter grupos do vendedor
          const gruposVendedor = gruposDeLeads.porVendedor[v.id] || {
            porPorte: {},
            porEstagio: {}
          };
          
          return {
            nome: v.nomeAbreviado || v.nome,
            leads: v.leads.length,
            maxLeads: maxLeadsDia,
            ocupacao: `${ocupacao}%`,
            percentualTotal: `${percentualTotal}%`,
            leadsLowScore: v.leads.filter(l => l.leadsLowScore).length,
            mediaScore: mediaScoreVendedor,
            leadsComScore: vendedorScore.count,
            grupos: gruposVendedor
          };
        }),
      leadsPorHora: generateLeadsByHour(vendedores, timezone)
    };
    
    return {
      relatorio,
      markdown: generateMarkdownReport(relatorio)
    };
  } catch (error) {
    logger.error("Erro ao gerar relatório de distribuição:", error);
    throw new Error("Erro ao gerar relatório de distribuição");
  }
}

/**
 * Gera dados de leads por hora
 */
function generateLeadsByHour(vendedores: any[], timezone: string) {
  // Agrupar leads por hora
  const leadsPorHora: Record<string, number> = {};
  vendedores.forEach(v => {
    v.leads.forEach((lead: any) => {
      const data = new Date(lead.createdAt);
      const hora = data.toLocaleTimeString('pt-BR', { 
        timeZone: timezone,
        hour: '2-digit'
      });
      
      leadsPorHora[hora] = (leadsPorHora[hora] || 0) + 1;
    });
  });
  
  // Ordenar as horas
  const horasOrdenadas = Object.keys(leadsPorHora).sort((a, b) => {
    return parseInt(a) - parseInt(b);
  });
  
  return horasOrdenadas.map(hora => ({
    hora: `${hora}h`,
    leads: leadsPorHora[hora]
  }));
}

/**
 * Gera o relatório formatado em markdown sem tabelas para Discord
 */
function generateMarkdownReport(relatorio: any) {
  // Período analisado
  let markdown = '';
  if (relatorio.periodo.inicio.includes('00:00') && relatorio.periodo.fim.includes('23:59')) {
    markdown += `# 📊 Distribuição do dia ${relatorio.periodo.inicio.split(' ')[0]}\n\n`;
  } else {
    markdown += `# 📊 Distribuição ${relatorio.periodo.inicio} até ${relatorio.periodo.fim}\n\n`;
  }
  
  // Resumo compacto
  markdown += `## Resumo\n`;
  
  // Linha única para leads e fallback
  let resumoLeads = `• **Leads:** ${relatorio.resumo.leadsDistribuidos}`;
  if (relatorio.resumo.leadsEmFallback > 0) {
    resumoLeads += ` (+${relatorio.resumo.leadsEmFallback} fallback)`;
  }
  if (relatorio.resumo.leadsIndicados > 0) {
    resumoLeads += ` • ${relatorio.resumo.leadsIndicados} indicados`;
  }
  markdown += `${resumoLeads}\n`;
  
  // Linha única para score
  if (relatorio.resumo.leadsComScore > 0) {
    let resumoScore = `• **Score:** ${relatorio.resumo.mediaScore}`;
    if (relatorio.resumo.leadsLowScore > 0) {
      resumoScore += ` (${relatorio.resumo.leadsLowScore} baixo)`;
    }
    markdown += `${resumoScore}\n`;
  }
  
  markdown += `\n`;
  
  // Distribuição por vendedor
  markdown += `### Vendedores\n`;
  
  relatorio.vendedores.forEach((v: any) => {
    // Linha principal do vendedor
    let vendedorLinha = `**${v.nome}** • ${v.leads} leads`;
    if (v.ocupacao !== '0%') vendedorLinha += ` (${v.ocupacao} pote)`;
    if (v.leadsComScore > 0) vendedorLinha += ` • Score ${v.mediaScore}`;
    markdown += vendedorLinha + `\n`;

    // Linha de porte (se houver)
    if (v.grupos?.porPorte && Object.keys(v.grupos.porPorte).length > 0) {
      const portes = Object.entries(v.grupos.porPorte)
        .sort(([, a], [, b]) => (b as number) - (a as number))
        .map(([porte, qtd]) => `${porte}: ${qtd}`)
        .join(' | ');
      markdown += `└─ Porte: ${portes}\n`;
    }

    // Linha de estágio (se houver)
    if (v.grupos?.porEstagio && Object.keys(v.grupos.porEstagio).length > 0) {
      const estagios = Object.entries(v.grupos.porEstagio)
        .sort(([, a], [, b]) => (b as number) - (a as number))
        .map(([estagio, qtd]) => `${estagio}: ${qtd}`)
        .join(' | ');
      markdown += `└─ Estágio: ${estagios}\n`;
    }

    markdown += `\n`;
  });
  
  // Totais por grupo
  if (relatorio.grupos.porPorte.length > 0 || relatorio.grupos.porEstagio.length > 0) {
    markdown += `### Totais por Grupo\n`;
    
    if (relatorio.grupos.porPorte.length > 0) {
      markdown += `**Porte:** `;
      markdown += relatorio.grupos.porPorte
        .map((item: any) => `${item.porte} (${item.quantidade})`)
        .join(', ');
      markdown += `\n`;
    }

    if (relatorio.grupos.porEstagio.length > 0) {
      markdown += `**Estágio:** `;
      markdown += relatorio.grupos.porEstagio
        .map((item: any) => `${item.estagio} (${item.quantidade})`)
        .join(', ');
      markdown += `\n`;
    }
  }

  // Distribuição por horário
  const horasRelevantes = relatorio.leadsPorHora.filter((item: any) => item.leads > 0);
  if (horasRelevantes.length > 0) {
    markdown += `\n### Distribuição Horária\n`;
    markdown += horasRelevantes
      .map((item: any) => `${item.hora}: ${item.leads}`)
      .join(' • ');
  }
  
  return markdown;
}
