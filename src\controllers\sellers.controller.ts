import { Request, Response } from "express";
import { <PERSON>rismaClient, Vended<PERSON>, Lead } from "@prisma/client";
import { updateSellersFromSheets } from "../utils/sellers.utils";
import { distributeLead } from "../utils/distribution.utils";

const prisma = new PrismaClient();

export const registerSellerController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const {
      nome,
      nomeAbreviado,
      maxLeadsDia,
      horarioEntrada,
      horarioSaida,
      leadsExtras,
      foraDoHorario,
      ownerId,
    } = req.body;
    const novoVendedor = await prisma.vendedor.create({
      data: {
        nome,
        nomeAbreviado,
        maxLeadsDia,
        horarioEntrada,
        horarioSaida,
        leadsExtras,
        foraDoHorario,
        ownerId,
      },
    });
    res.json(novoVendedor);
  } catch (error) {
    console.error("Erro ao criar vendedor:", error);
    res.status(500).json({ error: "Erro ao criar vendedor" });
  }
};
export const updateSellerController = async (
  req: Request,
  res: Response
): Promise<any> => {
  const sellerId = req.params.id; // Obtém o ID do vendedor da URL
  const {
    nome,
    nomeAbreviado,
    maxLeadsDia,
    ativo,
    horarioEntrada,
    horarioSaida,
    leadsExtras,
    foraDoHorario,
    ownerId,
    appPass,
  } = req.body;

  try {
    // Verifica se o vendedor existe
    const vendedor = await prisma.vendedor.findUnique({
      where: { id: sellerId },
    });

    if (!vendedor) {
      return res.status(404).json({ error: "Vendedor não encontrado" });
    }

    // Atualiza o vendedor com os novos dados
    const updatedSeller = await prisma.vendedor.update({
      where: { id: sellerId },
      data: {
        nome,
        nomeAbreviado,
        maxLeadsDia,
        ativo,
        horarioEntrada,
        horarioSaida,
        leadsExtras,
        foraDoHorario,
        ownerId,
        appPass,
      },
    });

    res.json(updatedSeller);
  } catch (error) {
    console.error("Erro ao atualizar vendedor:", error);
    res.status(500).json({ error: "Erro ao atualizar vendedor" });
  }
};
export const updateSellersController = async (
  req: Request,
  res: Response
): Promise<any> => {
  const sellers = req.body; // Obtém os vendedores do corpo da solicitação

  try {
    // Percorre cada vendedor e atualiza seus dados
    const updatedSellers = await Promise.all(
      sellers.map(async (seller: any) => {
        const sellerId = seller.id;

        // Verifica se o vendedor existe
        const existingSeller = await prisma.vendedor.findUnique({
          where: { id: sellerId },
        });

        if (!existingSeller) {
          return { id: sellerId, error: "Vendedor não encontrado" };
        }

        // Atualiza o vendedor com os novos dados
        return prisma.vendedor.update({
          where: { id: sellerId },
          data: {
            nome: seller.nome,
            nomeAbreviado: seller.nomeAbreviado,
            maxLeadsDia: seller.maxLeadsDia,
            ativo: seller.ativo,
            horarioEntrada: seller.horarioEntrada,
            horarioSaida: seller.horarioSaida,
            leadsExtras: seller.leadsExtras,
            foraDoHorario: seller.foraDoHorario,
            ownerId: seller.ownerId,
          },
        });
      })
    );

    res.json(updatedSellers);
  } catch (error) {
    console.error("Erro ao atualizar vendedores:", error);
    res.status(500).json({ error: "Erro ao atualizar vendedores" });
  }
};


export const getSellersController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const timezone = "America/Sao_Paulo";
    const today = new Date();

    // Define o início do intervalo: 20h do dia anterior
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 1,
      20,
      0,
      0
    );

    // Define o fim do intervalo: 20h do dia atual
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      20,
      0,
      0
    );

    // Método correto para converter para UTC
    // Usa o formato ISO para garantir conversão precisa
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const startOfDayIso = startOfDayUtc.toISOString();
    const endOfDayIso = endOfDayUtc.toISOString();
    
    console.log("Intervalo de busca:", startOfDay, endOfDay);
    console.log("Intervalo UTC:", startOfDayIso, endOfDayIso);
    
    // Busca os vendedores e seus leads dentro do intervalo
    const vendedores = await prisma.vendedor.findMany({
      include: {
        leads: {
          where: {
            AND: [
              { createdAt: { gte: startOfDayIso } },
              { createdAt: { lte: endOfDayIso } },
            ],
          },
        },
      },
      orderBy: { id: "asc" },
    });

    // Verificar quantos leads foram encontrados no total
    const totalLeads = vendedores.reduce((sum, vendedor) => sum + vendedor.leads.length, 0);
    console.log(`Total de leads encontrados: ${totalLeads}`);

    // Atualizar os contadores leadsRecebidosHoje com a contagem real
    const vendedoresAtualizados = await Promise.all(
      vendedores.map(async (vendedor) => {
        const leadsCount = vendedor.leads.length;
        
        // Se a contagem de leads for diferente do valor armazenado, atualizar no banco
        if (leadsCount !== vendedor.leadsRecebidosHoje) {
          console.log(`Atualizando contador para ${vendedor.nome}: ${vendedor.leadsRecebidosHoje} → ${leadsCount} leads`);
          
          // Atualizar no banco
          await prisma.vendedor.update({
            where: { id: vendedor.id },
            data: { leadsRecebidosHoje: leadsCount }
          });
          
          // Atualizar no objeto que será retornado
          vendedor.leadsRecebidosHoje = leadsCount;
        }
        return vendedor;
      })
    );

    // Serializar a resposta para ajustar o fuso horário
    const serializedVendedores = vendedoresAtualizados.map((vendedor) => ({
      ...vendedor,
      leads: vendedor.leads.map((lead) => ({
        ...lead,
        // Converte o `createdAt` para o fuso horário "America/Sao_Paulo"
        createdAt: new Date(lead.createdAt).toLocaleString("pt-BR", {
          timeZone: timezone,
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        }),
      })),
    }));

    res.json(serializedVendedores);
  } catch (error) {
    console.error("Erro ao buscar vendedores:", error);
    res.status(500).json({ error: "Erro ao buscar vendedores" });
  }
};

export const updateSellerCountController = async (
  req: Request,
  res: Response
): Promise<any> => {
  const sellerId = req.params.id; // Obtém o ID do vendedor da URL
  const { leadsRecebidosHoje } = req.body;

  try {
    // Verifica se o vendedor existe
    const vendedor = await prisma.vendedor.findUnique({
      where: { id: sellerId },
    });

    if (!vendedor) {
      return res.status(404).json({ error: "Vendedor não encontrado" });
    }

    // Atualiza o vendedor com os novos dados
    const updatedSeller = await prisma.vendedor.update({
      where: { id: sellerId },
      data: {
        leadsRecebidosHoje,
      },
    });

    res.json(updatedSeller);
  } catch (error) {
    console.error("Erro ao atualizar vendedor:", error);
    res.status(500).json({ error: "Erro ao atualizar vendedor" });
  }
};
export const deleteSellerController = async (req: Request, res: Response):Promise<any> => {
  const sellerId = req.params.id;

  try {
    // Verifica se o vendedor existe
    const vendedor = await prisma.vendedor.findUnique({
      where: { id: sellerId },
    });

    if (!vendedor) {
      return res.status(404).json({ error: "Vendedor não encontrado" });
    }

    // Deleta o vendedor do banco de dados
    await prisma.vendedor.delete({
      where: {
        id: sellerId,
      },
    });

    return res.status(200).json({ message: "Vendedor deletado com sucesso" });
  } catch (error) {
    console.error("Erro ao deletar vendedor:", error);
    res.status(500).json({ error: "Erro ao deletar vendedor" });
  }
};

export const deleteLeadController = async (
  req: Request,
  res: Response
): Promise<any> => {
  const leadData = req.body;
  const leadId = leadData.id.toString();

  try {
    const lead = await prisma.lead.findFirst({
      where: {
        crmId: leadId,
      },
      select: {
        id: true,
        vendedor: true,
        vendedorId: true,
      },
    });
    if (!lead) {
      return res.status(404).json({ error: "Lead não encontrado." });
    }
    // Deletar o lead do banco de dados
    await prisma.lead.delete({
      where: {
        id: lead.id,
      },
    });

    if (lead && lead.vendedorId) {
      await prisma.vendedor.update({
        where: {
          id: lead.vendedorId,
        },
        data: {
          leadsRecebidosHoje: {
            decrement: 1,
          },
        },
      });
    }

    return res.status(200).json({ lead: lead });
  } catch (error: any) {
    console.error("Erro ao deletar o lead:", error);
    return res.status(500).json({ error: "Erro ao deletar o lead." });
  }
};
// Controlador para buscar os leads do dia de um vendedor
export const getTodaysLeadsController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params; // Recebe o ID do vendedor como parâmetro

    const timezone = "America/Sao_Paulo";
    const today = new Date();

    // Define o início do dia às 00:00 no horário "America/Sao_Paulo"
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      0,
      0,
      0
    );

    // Define o final do dia às 20:00 no horário "America/Sao_Paulo"
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      20,
      0,
      0
    );

    // Converte startOfDay e endOfDay para UTC
    const startOfDayUtc = new Date(
      new Intl.DateTimeFormat("sv-SE", {
        timeZone: timezone,
        hour12: false,
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      }).format(startOfDay)
    );

    const endOfDayUtc = new Date(
      new Intl.DateTimeFormat("sv-SE", {
        timeZone: timezone,
        hour12: false,
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      }).format(endOfDay)
    );

    // Buscar os leads do dia do vendedor usando o Prisma
    const leads = await prisma.lead.findMany({
      where: {
        vendedorId: id,
        createdAt: {
          gte: startOfDayUtc,
          lte: endOfDayUtc,
        },
      },
    });

    return res.json(leads);
  } catch (error) {
    console.error("Erro ao buscar leads do dia:", error);
    return res.status(500).json({ error: "Erro ao buscar leads do dia" });
  }
};

export const getSellerLeadsController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params; // Recebe o ID do vendedor como parâmetro

    // Buscar os leads do dia do vendedor usando o Prisma
    const leads = await prisma.lead.findMany({
      where: {
        vendedorId: id,
      },
      select: {
        id: true,
        nome: true,
        crmId: true,
        createdAt: true,
        vendedor: {
          select: { nome: true, id: true },
        },
      },
    });

    return res.json(leads);
  } catch (error) {
    console.error("Erro ao buscar leads do dia:", error);
    return res.status(500).json({ error: "Erro ao buscar leads do dia" });
  }
};
export const getAllLeadsController = async (req: Request, res: Response):Promise<any> => {
  try {
    // Buscar todos os leads com informações de nome do vendedor, ID do vendedor, CRM ID e nome do lead
    const leads = await prisma.lead.findMany({
      select: {
        vendedor: {
          select: {
            nome: true,
          },
        },
        createdAt: true,
        crmId: true,
        nome: true,
      },
    });
    return res.json(leads);
  } catch (error) {
    console.error("Erro ao buscar todos os leads:", error);
    return res.status(500).json({ error: "Erro ao buscar todos os leads" });
  }
};

export const resetCountController = async (req: Request, res: Response):Promise<any> => {
  await prisma.vendedor.updateMany({
    data: { leadsRecebidosHoje: 0 },
  });
  
  return res
    .status(200)
    .json({ message: "Contagem de leads resetada com sucesso" });
};
export const updateBySheetsSellersController = async (
  req: Request,
  res: Response
): Promise<any> => {
  console.log("Recebendo a requisição no Lead Hub a partir da planilha base para atualizar Vendedores...");
  try {
    const result = await updateSellersFromSheets();
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
export const updateSellerToken = async (req: Request, res: Response):Promise<any> => {
  const sellerId = req.params.id;
  const token = req.body.token;
  try {
    await prisma.vendedor.update({
      where: { id: sellerId },
      data: { appToken: token },
    });
    return res.status(200).json({ message: "Token atualizado com sucesso" });
  } catch (error) {
    return res.status(500).json({ error: "Erro ao atualizar token" });
  }
};
export const sellerAppLogin = async (req: Request, res: Response):Promise<any> => {
  const { ownerId, password } = req.body;
  try {
    const seller = await prisma.vendedor.findFirst({
      where: { ownerId: parseInt(ownerId) },
      select: {
        id: true,
        ownerId: true,
        appPass: true,
        leadsRecebidosHoje: true,
        leads: true,
        nomeAbreviado: true,
      },
    });

    if (!seller) {
      return res.status(404).json({ error: "Vendedor não encontrado" });
    }

    if (seller.appPass !== password) {
      return res.status(401).json({ error: "Senha inválida" });
    }

    return res.status(200).json({
      id: seller.id,
      success: true,
      leads: seller.leads,
      leadsRecebidosHoje: seller.leadsRecebidosHoje,
      nomeAbreviado: seller.nomeAbreviado,
    });
  } catch (error) {
    console.error(error);
    return res
      .status(500)
      .json({ error: "Erro ao realizar login", success: false });
  }
};

export const reassignLeadController = async (req: Request, res: Response):Promise<any> => {
  const leadData = req.body;
  const leadId = leadData.id.toString();
  const stageIdToMove = 432437; // Certifique-se de que esse ID é o correto
  const spreadsheetId = "1HRn5UJJnLuiZb6pb9fNwvDAGDR6O3FCZkz5fBz7pE-s"; // Defina ou recupere conforme necessário

  try {
    console.log(`Iniciando re-delegação do lead: ${leadId}`);

    // 1. Buscar o lead no banco para verificar se existe e obter o vendedor atual
    const lead = await prisma.lead.findFirst({
      where: { crmId: leadId },
      select: {
        id: true,
        vendedor: {
          select: {
            id: true,
            nomeAbreviado: true,
          },
        },
        vendedorId: true,
      },
    });

    if (!lead) {
      console.warn(`Lead ${leadId} não encontrado.`);
      return res.status(404).json({ error: "Lead não encontrado." });
    }

    // 2. Se o lead estiver atribuído a um vendedor, decrementar a contagem e deletar o lead dentro de uma transação
    if (lead.vendedorId) {
      console.log(
        `Decrementando contagem do vendedor: ${lead.vendedor?.nomeAbreviado}`
      );

      await prisma.$transaction(async (prismaTransaction) => {
        if (lead.vendedorId) {
          await prismaTransaction.vendedor.update({
            where: { id: lead.vendedorId },
            data: { leadsRecebidosHoje: { decrement: 1 } },
          });
        }
        await prismaTransaction.lead.delete({ where: { id: lead.id } });
      });

      console.log(
        `Contagem do vendedor ${lead.vendedor?.nomeAbreviado} decrementada e lead deletado.`
      );
    } else {
      // Se o lead não estava atribuído a nenhum vendedor, apenas deletar
      await prisma.lead.delete({ where: { id: lead.id } });
      console.log(`Lead ${leadId} deletado do banco de dados.`);
    }

    // 3. Redistribuir o lead usando a lógica de distribuição
    console.log(`Redistribuindo o lead ${leadId}...`);

    const oldVendor = lead.vendedor
      ? {
          id: lead.vendedor.id,
          nomeAbreviado: lead.vendedor.nomeAbreviado,
        }
      : undefined;

    // Reutilizar o mesmo leadData para redistribuição
    const distributionResult = await distributeLead(
      leadData,
      stageIdToMove,
      spreadsheetId,
      oldVendor
    );

    return res.status(200).json(distributionResult);
  } catch (error: any) {
    console.error(`Erro ao re-delegar o lead ${leadId}:`, error);
    return res.status(500).json({ error: "Erro ao re-delegar o lead." });
  }
};

export const setActiveSellerController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const sellerId = req.query.id as string | undefined;
    
    // Se nenhum ID for fornecido, ativar todos os vendedores
    if (!sellerId) {
      await prisma.vendedor.updateMany({
        data: { ativo: true }
      });
      
      return res.status(200).json({
        success: true,
        message: "Todos os vendedores foram ativados com sucesso"
      });
    }
    
    // Verificar se o vendedor existe
    const vendedor = await prisma.vendedor.findUnique({
      where: { id: sellerId }
    });
    
    if (!vendedor) {
      return res.status(404).json({
        success: false,
        message: "Vendedor não encontrado"
      });
    }
    
    // Transação para garantir que todas as atualizações sejam concluídas
    await prisma.$transaction([
      // Desativar todos os vendedores
      prisma.vendedor.updateMany({
        data: { ativo: false }
      }),
      
      // Ativar apenas o vendedor especificado
      prisma.vendedor.update({
        where: { id: sellerId },
        data: { ativo: true }
      })
    ]);
    
    return res.status(200).json({
      success: true,
      message: `Apenas o vendedor ${vendedor.nome} está ativo agora`
    });
  } catch (error) {
    console.error("Erro ao definir vendedor ativo:", error);
    return res.status(500).json({
      success: false,
      message: "Erro ao definir vendedor ativo",
      error: (error as Error).message
    });
  }
};
