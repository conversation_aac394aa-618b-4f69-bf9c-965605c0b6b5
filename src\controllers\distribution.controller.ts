import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import {
  filterAndSortVendedores,
  findValueByName,
  getLeadFromPipeRun,
  selectVendor,
  sentNotification,
  updateGoogleSheet,
  updateLeadInPipeRun,
} from "../utils/distribution.utils";
import { createLead, generateDistributionReport } from "../services/distribution.service";
import { filterVendedoresByInterval } from "../utils/sellers.utils";
import * as config from "../config";
import axios from "axios";
import dotenv from "dotenv";
import { logger } from "../utils/logger.utils";
import path from "path";
import fs from "fs";
dotenv.config();

const prisma = new PrismaClient();

const spreadsheetId = "1HRn5UJJnLuiZb6pb9fNwvDAGDR6O3FCZkz5fBz7pE-s";

export const distributionController = async (
  req: Request,
  res: Response
): Promise<any> => {
  logger.info("Iniciando distributionController...");

  const leadData = req.body;
  const stageIdToMove = 432437;
  const leadId = leadData.id.toString();

  logger.leadReceived(leadId, leadData.title, leadData);

  // Processamento dos dados do lead
  const leadScore = findValueByName(leadData, "Lead Score");
  const porte = findValueByName(leadData, "porte") || "";

  function isValidJson(str: string): boolean {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }
  const porteArray = isValidJson(porte) ? JSON.parse(porte) : [];

  const isLowScoreLead: boolean =
    (porteArray.length > 0 && porteArray[0] === "MEI") ||
    (leadScore !== null && leadScore !== "" && Number(leadScore) < 70);

  const normalizeText = (text: string) =>
    text.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

  const leadOrigem = normalizeText(leadData.origin?.name || "");
  const leadIndicado = leadOrigem.toLowerCase().includes("indicacao");

  logger.leadCharacteristics(leadId, isLowScoreLead, leadIndicado, leadScore);

  try {
    // Verificar se o lead já está atribuído a um vendedor
    const existingLead = await prisma.lead.findFirst({
      where: { crmId: leadId }, // Ajuste conforme seu schema
      include: { vendedor: true },
    });

    if (existingLead && existingLead.vendedor?.ownerId) {
      logger.info(
        `Lead já está atribuído ao vendedor: ${existingLead?.vendedor?.nomeAbreviado}`
      );

      // Atualizar o lead no PipeRun
      await updateLeadInPipeRun(
        leadId,
        stageIdToMove,
        existingLead.vendedor.ownerId,
        true // Não mover o lead.
      );
      
      logger.crmUpdate(leadId, existingLead.vendedor.ownerId);

      // Atualizar a planilha do Google Sheets
      await updateGoogleSheet(spreadsheetId, existingLead.vendedor);
      
      logger.info(`PLANILHA ATUALIZADA: ${existingLead.vendedor.nomeAbreviado || 'N/A'}`, { leadsRecebidosHoje: existingLead.vendedor.leadsRecebidosHoje || 0 });

      const message = {
        type: "NOVO LEAD",
        title: leadData.title,
        name: leadData.user.name,
        leadScore: leadScore,
        crmLink: `https://app.pipe.run/pipeline/gerenciador/visualizar/${leadId}`,
      };

      if (
        existingLead.vendedor?.ownerId &&
        existingLead.vendedor?.nomeAbreviado
      ) {
        logger.info(
          `Tentando enviar notificação para lead existente: ${existingLead.vendedor.nomeAbreviado}`
        );

        try {
          sentNotification(
            existingLead.vendedor?.nomeAbreviado,
            existingLead.vendedor?.ownerId.toString(),
            JSON.stringify(message)
          );
          logger.notification(existingLead.vendedor.nomeAbreviado, message);
        } catch (notifError) {
          logger.error(
            `Falha ao enviar notificação para lead existente: ${existingLead.vendedor.nomeAbreviado}`,
            notifError
          );
        }
      }

      logger.distributionComplete(leadId, existingLead.vendedor?.nomeAbreviado || 'N/A');

      return res.status(200).json({
        message: `Lead já atribuído ao vendedor ${existingLead.vendedor.nome}. Atualizações realizadas com sucesso!`,
        lead: existingLead,
      });
    }

    // Se o lead não estiver atribuído, continuar com a lógica existente
    let allVendedores: any[] = await prisma.vendedor.findMany({
      where: { ativo: true },
      include: { leads: true },
      orderBy: { leadsRecebidosHoje: "asc" }
    });

    // 1. Primeiro filtramos por horário
    allVendedores = await filterAndSortVendedores(allVendedores);
    
    logger.availableVendors(allVendedores);

    // 2. Se houver vendedores disponíveis, aí sim aplicamos o filtro de intervalo
    if (allVendedores.length > 0) {
      const vendedoresNoIntervalo = await filterVendedoresByInterval(
        allVendedores, 
        spreadsheetId
      );

      // Se o filtro de intervalo retornar vendedores, usamos eles
      // Senão, mantemos os vendedores filtrados apenas por horário
      if (vendedoresNoIntervalo.length > 0) {
        logger.info("Aplicando filtro de intervalo entre leads", {
          antes: allVendedores.length,
          depois: vendedoresNoIntervalo.length
        });
        allVendedores = vendedoresNoIntervalo;
      }
    }

    logger.availableVendors(allVendedores);

    const selectedVendor = await selectVendor(
      allVendedores,
      leadIndicado,
      isLowScoreLead
    );

    if (selectedVendor.selectedVendorId === null) {
      logger.fallback(leadId, "Nenhum vendedor disponível para atender os critérios");
      const lead = await createLead(
        leadData.id.toString(),
        leadData.title,
        undefined,
        true,
        Number(leadScore),
        leadOrigem,
        leadIndicado,
        isLowScoreLead
      );
      return res.status(404).json({
        message: `Nenhum vendedor disponível. Lead marcado como em fallback. ${lead}`,
      });
    }

    const { selectedVendorId, selectedVendorOwnerId, nomeAbreviado, vendedorNome } = selectedVendor;
    
    // Armazenar critérios de seleção
    const criterios = [];
    if (isLowScoreLead) criterios.push("Lead de baixo score");
    if (leadIndicado) criterios.push("Lead indicado");
    
    // IDs dos vendedores prioritários para leads de baixo score
    const vendedoresPrioritariosIds = [
      '3cc41e34-68f6-49ac-bc3d-55c47b6349fb',
      'fefd9ac4-b262-4608-b017-9ae4a513609e',
      'ad03d0d8-9643-422d-ab58-2222a20931a6',
      'ed0764ec-3a99-4f25-ad36-79d716c2b30e'
    ];
    
    logger.vendorSelected(leadId, { 
      id: selectedVendorId, 
      nome: vendedorNome, 
      nomeAbreviado 
    }, criterios);

    // Em vez de usar allVendedores.find, vamos obter dados diretamente do banco
    let vendedorAtualizado;
    try {
      // Consulta direta ao banco para obter dados 100% atualizados para os logs
      vendedorAtualizado = await prisma.vendedor.findUnique({
        where: { id: selectedVendorId || '' },
        select: { 
          leadsRecebidosHoje: true, 
          maxLeadsDia: true, 
          nomeAbreviado: true 
        }
      });
      
      // Se não encontrar no banco, usar valores padrão
      if (!vendedorAtualizado) {
        logger.warning(`Vendedor ${selectedVendorId} não encontrado no banco para logs.`);
        vendedorAtualizado = { 
          leadsRecebidosHoje: 0, 
          maxLeadsDia: 9, 
          nomeAbreviado: nomeAbreviado 
        };
      }
    } catch (error) {
      logger.error(`Erro ao buscar dados atualizados do vendedor ${selectedVendorId} para logs`, error);
      // Usar valores padrão em caso de erro
      vendedorAtualizado = { 
        leadsRecebidosHoje: 0, 
        maxLeadsDia: 9, 
        nomeAbreviado: nomeAbreviado 
      };
    }

    // Calcular usando os dados mais recentes do banco
    const leadsRecebidos = vendedorAtualizado.leadsRecebidosHoje || 0;
    const tamanhoPote = vendedorAtualizado.maxLeadsDia || 9;
    const porcentagemOcupacao = (tamanhoPote > 0) ? 
                               (leadsRecebidos / tamanhoPote * 100) : 0;
    
    // Determinar o motivo principal da seleção
    let motivoSelecao = "Vendedor com menor proporção de ocupação do pote";
    let criteriosChave: Record<string, any> = {
      ocupacao_pote: `${Math.round(porcentagemOcupacao)}%`,
      leads_recebidos: leadsRecebidos,
      tamanho_pote: tamanhoPote
    };
    
    if (isLowScoreLead && vendedoresPrioritariosIds.includes(selectedVendorId || '')) {
      motivoSelecao = "Vendedor prioritário para leads de baixo score";
      criteriosChave.tipo_lead = "Baixo score";
    } else if (leadIndicado) {
      motivoSelecao = "Vendedor aceita leads indicados";
      criteriosChave.tipo_lead = "Indicado";
    }
    
    logger.selectionSummary(leadId, { 
      nome: vendedorNome, 
      nomeAbreviado, 
      id: selectedVendorId 
    }, motivoSelecao, criteriosChave);

    // Atualizar o lead no PipeRun
    if (selectedVendorOwnerId) {
      await updateLeadInPipeRun(leadId, stageIdToMove, selectedVendorOwnerId);
      logger.crmUpdate(leadId, selectedVendorOwnerId);
    }

    // Criar um novo lead no banco de dados
    const lead = await createLead(
      leadData.id.toString(),
      leadData.title,
      selectedVendorId || "",
      false,
      Number(leadScore),
      leadOrigem,
      leadIndicado,
      isLowScoreLead
    );

    logger.info(`Lead criado no banco de dados: ${lead.id}`);

    // Variável para armazenar a contagem real
    let leadsCount = 0;

    // Atualizar o contador baseado na contagem real de leads
    if (selectedVendorId) {
      try {
        // Definir o intervalo de tempo (20h de ontem até 20h de hoje)
        const timezone = "America/Sao_Paulo";
        const today = new Date();
        
        // Início e fim do intervalo
        const startOfDay = new Date(
          today.getFullYear(),
          today.getMonth(), 
          today.getDate() - 1,
          20, 0, 0
        );
        
        const endOfDay = new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate(),
          20, 0, 0
        );
        
        // Conversão correta para UTC
        const startOfDayUtc = new Date(
          startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
        );
        
        const endOfDayUtc = new Date(
          endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
        );
        
        const startOfDayIso = startOfDayUtc.toISOString();
        const endOfDayIso = endOfDayUtc.toISOString();
        
        logger.info(`Buscando leads no intervalo: ${startOfDayIso} a ${endOfDayIso}`);
        
        // Buscar o vendedor com seus leads atuais usando uma consulta otimizada
        const vendedorAtualizado = await prisma.vendedor.findUnique({
          where: { id: selectedVendorId },
          include: {
            leads: {
              where: {
                AND: [
                  { createdAt: { gte: startOfDayIso } },
                  { createdAt: { lte: endOfDayIso } },
                ],
              },
            },
          },
        });
        
        if (vendedorAtualizado) {
          leadsCount = vendedorAtualizado.leads.length;
          
          // Atualizar o contador explicitamente com o valor real
          await prisma.vendedor.update({
            where: { id: selectedVendorId },
            data: { leadsRecebidosHoje: leadsCount }
          });
          
          logger.info(`Contador atualizado para ${nomeAbreviado}: ${leadsCount} leads (baseado na contagem real)`);
          
          // Garante que o objeto selectedVendor também tenha a contagem atualizada
          if (selectedVendor) {
            (selectedVendor as any).leadsRecebidosHoje = leadsCount;
          }
        } else {
          logger.warning(`Vendedor ${selectedVendorId} não encontrado ao atualizar contador`);
        }
      } catch (error) {
        logger.error(`Erro ao atualizar contador para ${nomeAbreviado}`, error);
        // Mesmo com erro, continuamos o fluxo
      }
    }

    // Atualizar a planilha do Google Sheets
    try {
      await updateGoogleSheet(spreadsheetId, selectedVendor);
      logger.info(`PLANILHA ATUALIZADA: ${nomeAbreviado || 'N/A'}`, { leadsRecebidosHoje: leadsCount });
    } catch (error) {
      logger.error(`Erro ao atualizar planilha para ${nomeAbreviado}`, error);
    }

    const message = {
      type: "NOVO LEAD",
      title: leadData.title,
      name: leadData.user.name,
      leadScore: leadScore,
      crmLink: `https://app.pipe.run/pipeline/gerenciador/visualizar/${leadId}`,
    };

    if (selectedVendor && selectedVendorOwnerId && nomeAbreviado) {
      logger.info(`Tentando enviar notificação para novo lead: ${nomeAbreviado}`);

      try {
        sentNotification(
          nomeAbreviado,
          selectedVendorOwnerId.toString(),
          JSON.stringify(message)
        );
        logger.notification(nomeAbreviado, message);
      } catch (notifError) {
        logger.error(
          `Falha ao enviar notificação para novo lead: ${nomeAbreviado}`,
          notifError
        );
      }
    }
    
    logger.distributionComplete(leadId, nomeAbreviado || 'N/A');

    return res.status(200).json({
      message: `Lead registrado com sucesso e atribuído ao vendedor ${nomeAbreviado}`,
      lead: lead,
    });
  } catch (error: any) {
    logger.error("Erro ao processar o lead", error);
    return res.status(500).json({ error: "Erro ao processar o lead" });
  }
};

export const getDistributionReportController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const timezone = "America/Sao_Paulo";
    const today = new Date();
    
    // Define o início do intervalo: 20h do dia anterior
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 1,
      20, 0, 0
    );

    // Define o fim do intervalo: 20h do dia atual
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      20, 0, 0
    );

    // Converter para UTC
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const startOfDayIso = startOfDayUtc.toISOString();
    const endOfDayIso = endOfDayUtc.toISOString();
    
    // Buscar vendedores e seus leads
    const vendedores = await prisma.vendedor.findMany({
      where: { ativo: true },
      include: {
        leads: {
          where: {
            AND: [
              { createdAt: { gte: startOfDayIso } },
              { createdAt: { lte: endOfDayIso } },
              { emFallback: false } // Excluir leads em fallback
            ],
          },
          orderBy: { createdAt: 'asc' }
        },
      },
      orderBy: { leadsRecebidosHoje: 'desc' }
    });
    
    // Buscar leads em fallback
    const leadsEmFallback = await prisma.lead.findMany({
      where: {
        AND: [
          { createdAt: { gte: startOfDayIso } },
          { createdAt: { lte: endOfDayIso } },
          { emFallback: true }
        ],
      },
    });
    
    // Calcular totais
    const totalLeads = vendedores.reduce((sum, v) => sum + v.leads.length, 0) + leadsEmFallback.length;
    const totalLeadsDistribuidos = totalLeads - leadsEmFallback.length;
    const vendedoresComLeads = vendedores.filter(v => v.leads.length > 0).length;
    const mediaLeadsPorVendedor = vendedoresComLeads > 0 ? 
      (totalLeadsDistribuidos / vendedoresComLeads).toFixed(1) : 0;
    
    // Calcular leads por tipo
    const leadsLowScore = vendedores.reduce((sum, v) => 
      sum + v.leads.filter(l => l.leadsLowScore).length, 0);
    const leadsIndicados = vendedores.reduce((sum, v) => 
      sum + v.leads.filter(l => l.indicado).length, 0);
    
    // Formatar data para título do relatório
    const dataRelatorio = new Date().toLocaleDateString('pt-BR', {
      timeZone: timezone,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    
    // Criar o relatório em markdown
    let markdown = `# 📊 Relatório de Distribuição de Leads (${dataRelatorio})\n\n`;
    
    // Adicionar resumo
    markdown += `## Resumo\n\n`;
    markdown += `- **Total de Leads**: ${totalLeads}\n`;
    markdown += `- **Leads Distribuídos**: ${totalLeadsDistribuidos}\n`;
    markdown += `- **Leads em Fallback**: ${leadsEmFallback.length}\n`;
    markdown += `- **Leads de Baixo Score**: ${leadsLowScore}\n`;
    markdown += `- **Leads Indicados**: ${leadsIndicados}\n`;
    markdown += `- **Vendedores Ativos**: ${vendedores.length}\n`;
    markdown += `- **Vendedores que Receberam Leads**: ${vendedoresComLeads}\n`;
    markdown += `- **Média de Leads por Vendedor**: ${mediaLeadsPorVendedor}\n\n`;
    
    // Tabela de distribuição por vendedor
    markdown += `## Distribuição por Vendedor\n\n`;
    markdown += `| Vendedor | Leads | Pote | Ocupação | % do Total |\n`;
    markdown += `|:---------|:-----:|:----:|:--------:|:----------:|\n`;
    
    // Ordenar vendedores por quantidade de leads
    const vendedoresOrdenados = [...vendedores]
      .filter(v => v.leads.length > 0)
      .sort((a, b) => b.leads.length - a.leads.length);
    
    vendedoresOrdenados.forEach(v => {
      const maxLeadsDia = typeof v.maxLeadsDia === 'number' ? v.maxLeadsDia : 0;
      const ocupacao = maxLeadsDia > 0 ? 
        Math.round((v.leads.length / maxLeadsDia) * 100) : 0;
      
      const percentualTotal = totalLeadsDistribuidos > 0 ?
        Math.round((v.leads.length / totalLeadsDistribuidos) * 100) : 0;
      
      markdown += `| ${v.nomeAbreviado || v.nome} | ${v.leads.length} | ${maxLeadsDia} | ${ocupacao}% | ${percentualTotal}% |\n`;
    });
    
    markdown += `\n`;
    
    // Distribuição de leads por horário
    markdown += `## Distribuição por Horário\n\n`;
    
    // Agrupar leads por hora
    const leadsPorHora: Record<string, number> = {};
    vendedores.forEach(v => {
      v.leads.forEach(lead => {
        const data = new Date(lead.createdAt);
        const hora = data.toLocaleTimeString('pt-BR', { 
          timeZone: timezone,
          hour: '2-digit'
        });
        
        leadsPorHora[hora] = (leadsPorHora[hora] || 0) + 1;
      });
    });
    
    // Ordenar as horas
    const horasOrdenadas = Object.keys(leadsPorHora).sort((a, b) => {
      return parseInt(a) - parseInt(b);
    });
    
    markdown += `| Hora | Leads Recebidos |\n`;
    markdown += `|:-----|:---------------:|\n`;
    
    horasOrdenadas.forEach(hora => {
      markdown += `| ${hora}h | ${leadsPorHora[hora]} |\n`;
    });
    
    // Leads de baixo score
    if (leadsLowScore > 0) {
      markdown += `\n## Leads de Baixo Score\n\n`;
      markdown += `| Vendedor | Leads de Baixo Score | % do Total |\n`;
      markdown += `|:---------|:--------------------:|:----------:|\n`;
      
      vendedoresOrdenados.forEach(v => {
        const lowScoreCount = v.leads.filter(l => l.leadsLowScore).length;
        if (lowScoreCount > 0) {
          const percentual = leadsLowScore > 0 ?
            Math.round((lowScoreCount / leadsLowScore) * 100) : 0;
          
          markdown += `| ${v.nomeAbreviado || v.nome} | ${lowScoreCount} | ${percentual}% |\n`;
        }
      });
    }
    
    // Fornecer o relatório como resposta
    res.setHeader('Content-Type', 'text/markdown');
    return res.status(200).send(markdown);
    
  } catch (error) {
    console.error("Erro ao gerar relatório:", error);
    return res.status(500).json({ error: "Erro ao gerar relatório de distribuição" });
  }
};

/**
 * Controlador para analisar os logs e diagnosticar problemas de distribuição de leads
 * Identifica motivos pelos quais um vendedor específico não recebeu leads
 */
export const getDistributionLogDiagnosticController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    // Pegar parâmetros da requisição
    const { vendedorNome, data } = req.query;
    
    if (!vendedorNome) {
      return res.status(400).json({ 
        error: "Nome do vendedor é obrigatório" 
      });
    }
    
    // Normalizar o nome do vendedor para busca case-insensitive
    const nomeNormalizado = String(vendedorNome).toLowerCase().trim();
    
    // Definir a data a ser analisada (hoje se não especificada)
    let dataAnalise: Date;
    if (data) {
      // Aceitar tanto DD-MM-YYYY quanto YYYY-MM-DD
      const partes = String(data).split(/[-/]/);
      if (partes.length !== 3) {
        return res.status(400).json({ 
          error: "Formato de data inválido. Use DD-MM-YYYY ou YYYY-MM-DD" 
        });
      }

      // Verificar qual formato foi usado baseado no primeiro número
      if (parseInt(partes[0]) > 31) {
        // Formato YYYY-MM-DD
        dataAnalise = new Date(parseInt(partes[0]), parseInt(partes[1]) - 1, parseInt(partes[2]));
      } else {
        // Formato DD-MM-YYYY
        dataAnalise = new Date(parseInt(partes[2]), parseInt(partes[1]) - 1, parseInt(partes[0]));
      }
      
      // Verificar se a data é válida
      if (isNaN(dataAnalise.getTime())) {
        return res.status(400).json({ 
          error: "Data inválida" 
        });
      }
    } else {
      dataAnalise = new Date();
    }

    // Ajustar para o fuso horário de São Paulo
    const timezone = "America/Sao_Paulo";
    
    // Definir início e fim do dia para busca (20h do dia anterior até 20h do dia atual)
    const startOfDay = new Date(dataAnalise);
    startOfDay.setDate(startOfDay.getDate() - 1);
    startOfDay.setHours(20, 0, 0, 0);
    
    const endOfDay = new Date(dataAnalise);
    endOfDay.setHours(20, 0, 0, 0);
    
    // Converter para UTC mantendo o horário correto
    const startOfDayUtc = new Date(
      startOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    const endOfDayUtc = new Date(
      endOfDay.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    // Formatar as datas para busca no log
    const dataInicio = startOfDayUtc.toISOString().split('T')[0];
    const dataFim = endOfDayUtc.toISOString().split('T')[0];
    
    // Caminho do arquivo de log
    const logFilePath = path.join(process.cwd(), 'logs', 'distribution.log');
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(logFilePath)) {
      return res.status(404).json({ 
        error: "Arquivo de log não encontrado" 
      });
    }
    
    // Ler o conteúdo do arquivo de log
    const logContent = fs.readFileSync(logFilePath, 'utf8');
    
    // Dividir o conteúdo por linhas e filtrar pelo período
    const linhasLog = logContent.split('\n')
      .filter(linha => {
        // Extrair a data da linha do log (formato: YYYY-MM-DD)
        const match = linha.match(/\d{4}-\d{2}-\d{2}/);
        if (!match) return false;
        
        const dataLog = match[0];
        return dataLog >= dataInicio && dataLog <= dataFim;
      });

    // Verificar se encontramos logs para o período
    if (linhasLog.length === 0) {
      return res.status(404).json({
        error: "Nenhum log encontrado para o período especificado",
        periodo: {
          inicio: dataInicio,
          fim: dataFim
        }
      });
    }

    // Resultados da análise
    const resultado = {
      vendedor: String(vendedorNome),
      data: dataInicio,
      leadsRecebidos: 0,
      leadsDistribuidos: 0,
      problemas: [] as string[],
      ocorrencias: [] as any[],
      vendedorInfo: null as any,
      estadoInicial: null as any
    };
    
    // Variáveis para armazenar informações relevantes durante a análise
    let vendedorEncontrado = false;
    let leadsDisponiveis = 0;
    let filtros: Record<string, number> = {};
    let decisoes: any[] = [];
    let detalhesVendedor: any = null;
    
    // Buscar o estado inicial do vendedor (primeira ocorrência nos logs)
    let estadoInicial = null;
    for (let i = 0; i < linhasLog.length; i++) {
      const linha = linhasLog[i];
      if (linha.includes('👨‍💼 VENDEDORES DISPONÍVEIS:')) {
        try {
          // Ler o JSON dos vendedores
          let jsonStr = '';
          let j = i + 1;
          while (j < linhasLog.length && !linhasLog[j].includes('LEAD RECEBIDO:')) {
            jsonStr += linhasLog[j];
            j++;
            if (jsonStr.trim().endsWith('}]')) break;
          }
          
          const vendedoresData = JSON.parse(jsonStr);
          const vendedorInfo = vendedoresData.find((v: any) => 
            v.nome && v.nome.toLowerCase().includes(nomeNormalizado) ||
            v.nomeAbreviado && v.nomeAbreviado.toLowerCase().includes(nomeNormalizado)
          );
          
          if (vendedorInfo) {
            estadoInicial = {
              timestamp: linha.split(' ')[0],
              ...vendedorInfo
            };
            resultado.estadoInicial = estadoInicial;
            
            // Se o vendedor já começou com o pote cheio, isso é um problema crítico
            const ocupacao = vendedorInfo.ocupacao || '0/0';
            const [atual, maximo] = ocupacao.split('/').map(Number);
            if (atual >= maximo) {
              resultado.problemas.unshift(`⚠️ Vendedor iniciou o período com pote cheio (${ocupacao} - ${vendedorInfo.porcentagem})`);
            }
            break;
          }
        } catch (error) {
          console.error("Erro ao analisar estado inicial:", error);
          continue;
        }
      }
    }

    // Analisar todas as ocorrências relacionadas ao vendedor
    let indice = 0;
    while (indice < linhasLog.length) {
      const linha = linhasLog[indice];
      
      // Verificar se é um lead recebido (início de uma distribuição)
      if (linha.includes('🔔 LEAD RECEBIDO:')) {
        leadsDisponiveis++;
        let leadId = "N/A";
        const matchId = linha.match(/LEAD RECEBIDO: #(\d+)/);
        if (matchId) {
          leadId = matchId[1];
        }
        
        // Avançar para ler o objeto JSON do lead (próxima linha)
        indice++;
        let leadData = null;
        try {
          // Continuar lendo e concatenando linhas até encontrar um objeto JSON válido
          let jsonStr = '';
          while (indice < linhasLog.length && !linhasLog[indice].includes('VENDEDORES DISPONÍVEIS:')) {
            jsonStr += linhasLog[indice];
            indice++;
          }
          leadData = JSON.parse(jsonStr);
        } catch (error) {
          // Se falhar ao analisar o JSON, voltar para o próximo lead
          indice++;
          continue;
        }
        
        // Procurar por linhas que mostram os vendedores disponíveis
        if (indice < linhasLog.length && linhasLog[indice].includes('VENDEDORES DISPONÍVEIS:')) {
          // Avançar para ler o objeto JSON dos vendedores disponíveis (próxima linha)
          indice++;
          let vendedoresData = null;
          try {
            let jsonStr = '';
            while (indice < linhasLog.length && !linhasLog[indice].includes('SELEÇÃO PARA LEAD')) {
              jsonStr += linhasLog[indice];
              indice++;
              if (jsonStr.trim().endsWith('}]')) break;
            }
            vendedoresData = JSON.parse(jsonStr);
          } catch (error) {
            // Se falhar ao analisar o JSON, voltar para o próximo lead
            indice++;
            continue;
          }
          
          // Verificar se a Leila está nos vendedores disponíveis para esse lead
          const vendedorInfo = vendedoresData.find((v: any) => 
            v.nome && v.nome.toLowerCase().includes(nomeNormalizado) ||
            v.nomeAbreviado && v.nomeAbreviado.toLowerCase().includes(nomeNormalizado)
          );
          
          if (vendedorInfo) {
            vendedorEncontrado = true;
            detalhesVendedor = vendedorInfo;
            
            // Verificar se o vendedor tinha o pote cheio
            if (vendedorInfo.leads_hoje >= vendedorInfo.pote) {
              resultado.problemas.push(`Pote cheio: ${vendedorInfo.leads_hoje}/${vendedorInfo.pote} leads`);
            }
            
            // Verificar horário
            if (vendedorInfo.fora_horario) {
              resultado.problemas.push(`Fora do horário de trabalho: ${vendedorInfo.horario}`);
            }
            
            // Verificar aceitação de tipos especiais de leads
            const isLowScore = (leadData?.data?.score !== null && 
              leadData?.data?.score !== undefined && 
              Number(leadData.data.score) < 70) || 
              (leadData?.data?.porte && leadData.data.porte[0] === "MEI");
              
            const isIndicado = leadData?.origem?.toLowerCase().includes("indicacao");
            
            if (isLowScore && !vendedorInfo.aceita_lowscore) {
              resultado.problemas.push(`Não aceita leads de baixo score (lead atual: ${leadData?.score || 'N/A'})`);
            }
            
            if (isIndicado && !vendedorInfo.aceita_indicado) {
              resultado.problemas.push("Não aceita leads indicados");
            }
            
            // Armazenar a ocorrência
            resultado.ocorrencias.push({
              leadId,
              timestamp: linha.split(' ')[0],
              status: 'disponível',
              ocupacaoPote: `${vendedorInfo.leads_hoje}/${vendedorInfo.pote}`,
              porcentagemOcupacao: vendedorInfo.ocupacao,
              horario: vendedorInfo.horario,
              aceita_lowscore: vendedorInfo.aceita_lowscore,
              aceita_indicado: vendedorInfo.aceita_indicado
            });
          } else {
            // Verificar se o vendedor foi filtrado em alguma etapa
            resultado.ocorrencias.push({
              leadId,
              timestamp: linha.split(' ')[0],
              status: 'filtrado',
              motivo: 'Não estava na lista de vendedores disponíveis'
            });
          }
        }
        
        // Procurar por linha que mostra o vendedor selecionado
        while (indice < linhasLog.length && !linhasLog[indice].includes('SELEÇÃO PARA LEAD')) {
          indice++;
        }
        
        if (indice < linhasLog.length && linhasLog[indice].includes('SELEÇÃO PARA LEAD')) {
          const vendedorSelecionado = linhasLog[indice].split(':')[2]?.split('-')[0]?.trim();
          
          if (vendedorSelecionado && vendedorSelecionado.toLowerCase().includes(nomeNormalizado)) {
            resultado.leadsRecebidos++;
          } else {
            // Verificar se o vendedor estava disponível mas perdeu na seleção
            if (vendedorEncontrado) {
              // Ler os critérios de seleção (próxima linha)
              indice++;
              let criterios = null;
              try {
                // Continuar lendo até encontrar o final do objeto JSON
                let jsonStr = '';
                while (indice < linhasLog.length && !jsonStr.includes('}')) {
                  jsonStr += linhasLog[indice];
                  indice++;
                }
                criterios = JSON.parse(jsonStr);
                
                // Adicionar a razão de outro vendedor ter sido selecionado
                if (criterios && criterios.criterios_selecao) {
                  decisoes.push({
                    leadId,
                    vendedorSelecionado,
                    criterios: criterios.criterios_selecao
                  });
                  
                  resultado.problemas.push(`Perdeu na seleção para ${vendedorSelecionado} pelos critérios: ${criterios.criterios_selecao.join(', ')}`);
                }
              } catch (error) {
                // Se falhar ao analisar os critérios, continuar
                indice++;
                continue;
              }
            }
          }
        }
      }
      
      // Verificar linhas de critérios específicos
      if (linha.includes('CRITÉRIO:')) {
        const criterio = linha.split('CRITÉRIO:')[1].trim();
        
        // Avançar para ler o objeto JSON do critério (próxima linha)
        indice++;
        let criterioData = null;
        try {
          let jsonStr = '';
          while (indice < linhasLog.length && !linhasLog[indice].includes('CRITÉRIO:') && !linhasLog[indice].includes('SELEÇÃO PARA LEAD')) {
            jsonStr += linhasLog[indice];
            indice++;
            if (jsonStr.trim().endsWith('}')) break;
          }
          criterioData = JSON.parse(jsonStr);
          
          // Verificar se o critério menciona o vendedor
          if (criterioData && criterioData.vendedores) {
            const mencionado = criterioData.vendedores.some((v: any) => 
              v.nome && v.nome.toLowerCase().includes(nomeNormalizado) ||
              v.nomeAbreviado && v.nomeAbreviado.toLowerCase().includes(nomeNormalizado)
            );
            
            if (mencionado) {
              // Verificar o tipo de critério para entender o filtro
              if (criterio.includes('Filtro por tipo de lead')) {
                filtros['tipo_lead'] = (filtros['tipo_lead'] || 0) + 1;
              } else if (criterio.includes('com menor proporção')) {
                filtros['proporcao'] = (filtros['proporcao'] || 0) + 1;
              } else if (criterio.includes('Vendedor escolhido')) {
                filtros['selecao_direta'] = (filtros['selecao_direta'] || 0) + 1;
              } else if (criterio.includes('Desempate')) {
                filtros['desempate'] = (filtros['desempate'] || 0) + 1;
              }
            }
          }
          
          continue;
        } catch (error) {
          // Se falhar ao analisar o JSON, voltar para a próxima linha
          indice++;
          continue;
        }
      }
      
      // Avançar para a próxima linha
      indice++;
    }
    
    // Completar resultados da análise
    resultado.leadsDistribuidos = resultado.ocorrencias.length;
    resultado.vendedorInfo = detalhesVendedor;
    
    // Se o vendedor nunca foi encontrado nos logs
    if (!vendedorEncontrado) {
      resultado.problemas.unshift("Vendedor não apareceu em nenhuma distribuição no período");
      
      // Verificar possíveis razões
      try {
        // Buscar dados completos do vendedor
        const vendedor = await prisma.vendedor.findFirst({
          where: {
            OR: [
              { nome: { contains: String(vendedorNome), mode: 'insensitive' } },
              { nomeAbreviado: { contains: String(vendedorNome), mode: 'insensitive' } }
            ]
          },
          include: {
            leads: {
              where: {
                AND: [
                  { createdAt: { gte: startOfDayUtc.toISOString() } },
                  { createdAt: { lte: endOfDayUtc.toISOString() } }
                ]
              }
            }
          }
        });
        
        if (!vendedor) {
          resultado.problemas.unshift("Vendedor não encontrado no banco de dados");
        } else {
          // Armazenar dados do vendedor para análise
          resultado.vendedorInfo = {
            nome: vendedor.nome,
            nomeAbreviado: vendedor.nomeAbreviado,
            ativo: vendedor.ativo,
            maxLeadsDia: vendedor.maxLeadsDia,
            leadsRecebidosHoje: vendedor.leadsRecebidosHoje,
            horarioEntrada: vendedor.horarioEntrada,
            horarioSaida: vendedor.horarioSaida,
            aceita_lowscore: vendedor.leadsLowScore,
            aceita_indicado: vendedor.leadsIndicados,
            aceita_extra: vendedor.leadsExtras
          };

          if (!vendedor.ativo) {
            resultado.problemas.unshift("Vendedor está inativo no sistema");
          } else {
            // Vendedor existe e está ativo, vamos detalhar possíveis problemas
            const problemas = [];
            
            // Verificar configuração de horário
            const agora = new Date();
            const horaAtual = agora.getHours();
            const minutoAtual = agora.getMinutes();
            const horaAtualFormatada = `${String(horaAtual).padStart(2, '0')}:${String(minutoAtual).padStart(2, '0')}`;
            
            if (vendedor.horarioEntrada && vendedor.horarioSaida) {
              const [horaEntrada, minutoEntrada] = vendedor.horarioEntrada.split(':').map(Number);
              const [horaSaida, minutoSaida] = vendedor.horarioSaida.split(':').map(Number);
              
              if (horaAtual < horaEntrada || (horaAtual === horaEntrada && minutoAtual < minutoEntrada) ||
                  horaAtual > horaSaida || (horaAtual === horaSaida && minutoAtual > minutoSaida)) {
                problemas.push(`Fora do horário de trabalho (Horário atual: ${horaAtualFormatada}, Horário configurado: ${vendedor.horarioEntrada}-${vendedor.horarioSaida})`);
              }
            } else {
              problemas.push("Horário de trabalho não configurado");
            }
            
            // Verificar pote de leads
            const leadsRecebidos = vendedor.leadsRecebidosHoje || 0;
            const maxLeads = vendedor.maxLeadsDia || 9;
            if (leadsRecebidos >= maxLeads) {
              problemas.push(`Pote de leads está cheio (${leadsRecebidos}/${maxLeads})`);
            }
            
            // Verificar configurações de tipos de leads
            const restricoes = [];
            if (!vendedor.leadsLowScore) restricoes.push("leads de baixo score");
            if (!vendedor.leadsIndicados) restricoes.push("leads indicados");
            if (!vendedor.leadsExtras) restricoes.push("leads extras");
            
            if (restricoes.length > 0) {
              problemas.push(`Não aceita: ${restricoes.join(', ')}`);
            }
            
            // Verificar se tem leads recebidos hoje
            if (vendedor.leads.length > 0) {
              const ultimoLead = vendedor.leads[vendedor.leads.length - 1];
              const tempoDesdeUltimoLead = new Date().getTime() - new Date(ultimoLead.createdAt).getTime();
              const minutosDesdeUltimoLead = Math.floor(tempoDesdeUltimoLead / (1000 * 60));
              
              if (minutosDesdeUltimoLead < 30) { // Assumindo intervalo de 30 minutos
                problemas.push(`Em intervalo de leads (${minutosDesdeUltimoLead} minutos desde o último lead)`);
              }
            }
            
            if (problemas.length > 0) {
              resultado.problemas.push(...problemas);
            } else {
              resultado.problemas.push("Vendedor está corretamente configurado mas não foi selecionado na distribuição");
            }
          }
        }
      } catch (error) {
        resultado.problemas.unshift("Erro ao verificar dados do vendedor no banco");
        console.error("Erro ao verificar vendedor:", error);
      }
    }
    
    // Adicionar informações sobre o período analisado no markdown
    let markdown = `# 🔍 Diagnóstico de Distribuição de Leads\n\n`;
    markdown += `## Vendedor: ${resultado.vendedor}\n`;
    markdown += `## Período Analisado\n`;
    markdown += `- **Início**: ${new Date(dataInicio).toLocaleString('pt-BR', { timeZone: timezone })}\n`;
    markdown += `- **Fim**: ${new Date(dataFim).toLocaleString('pt-BR', { timeZone: timezone })}\n\n`;
    
    markdown += `### Resumo\n\n`;
    markdown += `- **Leads Disponíveis**: ${leadsDisponiveis}\n`;
    markdown += `- **Participou da Distribuição**: ${vendedorEncontrado ? 'Sim' : 'Não'}\n`;
    markdown += `- **Leads Recebidos**: ${resultado.leadsRecebidos}\n\n`;
    
    if (resultado.problemas.length > 0) {
      markdown += `### Problemas Identificados\n\n`;
      resultado.problemas.forEach(problema => {
        markdown += `- ${problema}\n`;
      });
      markdown += `\n`;
    }
    
    if (resultado.vendedorInfo) {
      markdown += `### Configuração do Vendedor\n\n`;
      markdown += `- **Nome Completo**: ${resultado.vendedorInfo.nome}\n`;
      markdown += `- **Nome Abreviado**: ${resultado.vendedorInfo.nomeAbreviado || 'N/A'}\n`;
      markdown += `- **Status**: ${resultado.vendedorInfo.ativo ? '✅ Ativo' : '❌ Inativo'}\n`;
      markdown += `- **Horário**: ${resultado.vendedorInfo.horarioEntrada} - ${resultado.vendedorInfo.horarioSaida}\n`;
      markdown += `- **Pote de Leads**: ${resultado.vendedorInfo.leadsRecebidosHoje || 0}/${resultado.vendedorInfo.maxLeadsDia}\n`;
      markdown += `- **Aceita Low Score**: ${resultado.vendedorInfo.aceita_lowscore ? '✅ Sim' : '❌ Não'}\n`;
      markdown += `- **Aceita Indicados**: ${resultado.vendedorInfo.aceita_indicado ? '✅ Sim' : '❌ Não'}\n`;
      markdown += `- **Aceita Extras**: ${resultado.vendedorInfo.aceita_extra ? '✅ Sim' : '❌ Não'}\n\n`;
    }
    
    if (decisoes.length > 0) {
      markdown += `### Decisões de Distribuição\n\n`;
      markdown += `O vendedor perdeu na seleção para outros vendedores pelos seguintes critérios:\n\n`;
      
      decisoes.forEach(decisao => {
        markdown += `- **Lead ${decisao.leadId}**: Atribuído a ${decisao.vendedorSelecionado}\n`;
        markdown += `  Critérios: ${decisao.criterios.join(', ')}\n\n`;
      });
    }
    
    if (Object.keys(filtros).length > 0) {
      markdown += `### Filtros Aplicados\n\n`;
      markdown += `O vendedor foi mencionado nos seguintes filtros durante o processo de distribuição:\n\n`;
      
      Object.entries(filtros).forEach(([filtro, contagem]) => {
        let nomeAmigavel = filtro;
        
        switch(filtro) {
          case 'tipo_lead':
            nomeAmigavel = 'Filtro por tipo de lead';
            break;
          case 'proporcao':
            nomeAmigavel = 'Seleção por ocupação do pote';
            break;
          case 'selecao_direta':
            nomeAmigavel = 'Seleção direta';
            break;
          case 'desempate':
            nomeAmigavel = 'Desempate por tempo';
            break;
        }
        
        markdown += `- **${nomeAmigavel}**: ${contagem} ocorrências\n`;
      });
    }
    
    // Atualizar a seção de markdown para incluir o estado inicial
    if (resultado.estadoInicial) {
      markdown += `### Estado Inicial do Vendedor\n\n`;
      markdown += `- **Horário**: ${resultado.estadoInicial.timestamp}\n`;
      markdown += `- **Ocupação do Pote**: ${resultado.estadoInicial.ocupacao} (${resultado.estadoInicial.porcentagem})\n`;
      markdown += `- **Horário de Trabalho**: ${resultado.estadoInicial.horario || 'N/A'}\n`;
      if (resultado.estadoInicial.fora_horario) {
        markdown += `- ⚠️ **Fora do horário de trabalho**\n`;
      }
      markdown += `- **Aceita Low Score**: ${resultado.estadoInicial.aceita_lowscore ? '✅ Sim' : '❌ Não'}\n`;
      markdown += `- **Aceita Indicados**: ${resultado.estadoInicial.aceita_indicado ? '✅ Sim' : '❌ Não'}\n`;
      markdown += `- **Aceita Extras**: ${resultado.estadoInicial.aceita_extra ? '✅ Sim' : '❌ Não'}\n\n`;
    }
    
    // Fornecer o relatório como resposta
    res.setHeader('Content-Type', 'text/markdown');
    return res.status(200).send(markdown);
    
  } catch (error) {
    console.error("Erro ao analisar logs de distribuição:", error);
    return res.status(500).json({ 
      error: "Erro ao analisar logs de distribuição",
      details: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Controlador para gerar relatório de distribuição para uma data específica
 */
export const getDistributionReportByDateController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    // Pegar data da query
    const { data, periodo } = req.query;
    
    // Definir a data a ser analisada (hoje se não especificada)
    let dataAnalise: Date;
    if (data) {
      // Aceitar tanto DD-MM-YYYY quanto YYYY-MM-DD
      const partes = String(data).split(/[-/]/);
      if (partes.length !== 3) {
        return res.status(400).json({ 
          error: "Formato de data inválido. Use DD-MM-YYYY ou YYYY-MM-DD" 
        });
      }

      // Verificar qual formato foi usado baseado no primeiro número
      if (parseInt(partes[0]) > 31) {
        // Formato YYYY-MM-DD
        dataAnalise = new Date(parseInt(partes[0]), parseInt(partes[1]) - 1, parseInt(partes[2]));
      } else {
        // Formato DD-MM-YYYY
        dataAnalise = new Date(parseInt(partes[2]), parseInt(partes[1]) - 1, parseInt(partes[0]));
      }
      
      // Verificar se a data é válida
      if (isNaN(dataAnalise.getTime())) {
        return res.status(400).json({ 
          error: "Data inválida" 
        });
      }
    } else {
      dataAnalise = new Date();
    }

    console.log(`Relatório solicitado para: ${dataAnalise.toLocaleDateString()} (Parâmetro: ${data})`);
    
    // Verificar se foi solicitado um período específico de horas
    const usarPeriodoCompleto = periodo === 'completo';
    if (usarPeriodoCompleto) {
      // Definir o período para o dia completo (00:00 às 23:59)
      dataAnalise.setHours(12, 0, 0, 0); // Meio-dia para garantir o dia correto
      console.log(`Usando período completo do dia (00:00 às 23:59)`);
    } else {
      console.log(`Usando período padrão (20h anterior às 20h atual)`);
    }

    // Gerar o relatório usando o serviço
    const { relatorio, markdown } = await generateDistributionReport(dataAnalise, usarPeriodoCompleto);
    
    // Fornecer o relatório como resposta
    if (req.query.format === 'json') {
      return res.json(relatorio);
    } else {
      res.setHeader('Content-Type', 'text/markdown');
      return res.status(200).send(markdown);
    }
    
  } catch (error) {
    console.error("Erro ao gerar relatório:", error);
    return res.status(500).json({ error: "Erro ao gerar relatório de distribuição" });
  }
};

/**
 * Consulta a distribuição de leads do dia atual com foco nos vendedores ativos
 */
export async function getCurrentDistributionController(req: Request, res: Response): Promise<void> {
  try {
    // Obter data atual
    const hoje = new Date();
    const inicioDoDia = new Date(hoje);
    inicioDoDia.setHours(0, 0, 0, 0);

    // Buscar dados de distribuição do dia
    const prisma = new PrismaClient();
    
    // Buscar todos os vendedores ativos
    const vendedores = await prisma.vendedor.findMany({
      where: {
        ativo: true
      }
    });

    // Buscar todos os leads do dia
    const leads = await prisma.lead.findMany({
      where: {
        createdAt: {
          gte: inicioDoDia
        }
      }
    });

    // Agrupar leads por vendedor
    const leadsPorVendedor: Record<string, any[]> = {};
    vendedores.forEach(v => {
      leadsPorVendedor[v.id] = [];
    });

    leads.forEach(lead => {
      if (lead.vendedorId && leadsPorVendedor[lead.vendedorId]) {
        leadsPorVendedor[lead.vendedorId].push(lead);
      }
    });

    // Estatísticas gerais
    const totalLeadsHoje = leads.length;
    const leadsDistribuidos = leads.filter(lead => lead.vendedorId).length;
    const leadsEmFallback = leads.filter(lead => lead.emFallback).length;
    const leadsLowScore = leads.filter(lead => lead.leadsLowScore).length;
    const leadsIndicados = leads.filter(lead => lead.indicado).length;

    // Estatísticas por hora
    const leadsPorHora: Record<number, number> = {};
    leads.forEach(lead => {
      const hora = lead.createdAt.getHours();
      leadsPorHora[hora] = (leadsPorHora[hora] || 0) + 1;
    });

    // Formatar vendedores
    const vendedoresFormatados = vendedores.map(v => {
      const leadsVendedor = leadsPorVendedor[v.id] || [];
      const ocupacaoPote = v.maxLeadsDia ? Math.round((leadsVendedor.length / v.maxLeadsDia) * 100) : 0;
      
      return {
        id: v.id,
        nome: v.nomeAbreviado || v.nome,
        leadsHoje: leadsVendedor.length,
        maxLeadsDia: v.maxLeadsDia,
        ocupacaoPote: `${ocupacaoPote}%`,
        ativo: v.ativo,
        horarios: {
          entrada: v.horarioEntrada,
          saida: v.horarioSaida
        }
      };
    });

    // Ordenar vendedores por quantidade de leads
    vendedoresFormatados.sort((a, b) => b.leadsHoje - a.leadsHoje);

    // Formatar estatísticas por hora
    const horasFormatadas = Object.entries(leadsPorHora).map(([hora, quantidade]) => ({
      hora: `${hora}h`,
      leads: quantidade
    })).sort((a, b) => parseInt(a.hora) - parseInt(b.hora));

    // Resposta final
    res.json({
      dataConsulta: hoje.toLocaleString('pt-BR'),
      estatisticas: {
        totalLeads: totalLeadsHoje,
        leadsDistribuidos,
        leadsEmFallback,
        leadsLowScore,
        leadsIndicados,
        vendedoresAtivos: vendedores.length,
        vendedoresComLeads: vendedores.filter(v => leadsPorVendedor[v.id]?.length > 0).length
      },
      leadsPorHora: horasFormatadas,
      vendedores: vendedoresFormatados
    });
  } catch (error) {
    logger.error('Erro ao consultar distribuição atual:', error);
    res.status(500).json({ error: 'Erro ao consultar distribuição atual' });
  }
}
