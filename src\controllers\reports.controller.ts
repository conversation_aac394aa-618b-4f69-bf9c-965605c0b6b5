import { Request, Response } from "express";
import { generateDailyReport } from "../utils/report.utils";
import fs from 'fs';
import { logger } from "../utils/logger.utils";

/**
 * Controller para gerar manualmente o relatório diário
 */
export const generateReportController = async (req: Request, res: Response): Promise<void> => {
  try {
    logger.info("Solicitação manual de geração de relatório recebida");
    
    const reportPath = generateDailyReport();
    
    if (reportPath && fs.existsSync(reportPath)) {
      const reportContent = fs.readFileSync(reportPath, 'utf8');
      
      logger.info("Relatório gerado manualmente com sucesso", { path: reportPath });
      
      // Responder com o conteúdo do relatório
      res.status(200).json({
        success: true,
        message: "Relatório gerado com sucesso",
        reportPath,
        reportContent
      });
    } else {
      logger.error("Falha ao gerar relatório manual", { path: reportPath });
      
      res.status(500).json({
        success: false,
        message: "Não foi possível gerar o relatório",
        error: "Arquivo de relatório não encontrado"
      });
    }
  } catch (error) {
    logger.error("Erro ao gerar relatório manual", error);
    
    res.status(500).json({
      success: false,
      message: "Erro ao gerar relatório",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    });
  }
}; 