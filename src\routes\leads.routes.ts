import { Router } from "express";
import { deleteTestLeadsController, getFallbackLeads, updateLeadController } from "../controllers/leads.controller";
import { delegateLead } from "../controllers/leads.controller";



const leadsRouter = Router();
leadsRouter.delete("/deletar-leads-teste",deleteTestLeadsController) 
leadsRouter.put("/atualizar-lead/:crmId", updateLeadController);
leadsRouter.get("/buscarLeadsEmFallback", getFallbackLeads);
leadsRouter.post("/delegar-lead/:vendedorNome", delegateLead);

export default leadsRouter;
