/*
  Warnings:

  - The primary key for the `Vendedor` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropForeignKey
ALTER TABLE "Lead" DROP CONSTRAINT "Lead_vendedorId_fkey";

-- AlterTable
ALTER TABLE "Lead" ALTER COLUMN "vendedorId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Vendedor" DROP CONSTRAINT "Vendedor_pkey",
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "Vendedor_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "Vendedor_id_seq";

-- AddForeignKey
ALTER TABLE "Lead" ADD CONSTRAINT "Lead_vendedorId_fkey" FOREIGN KEY ("vendedorId") REFERENCES "Vendedor"("id") ON DELETE SET NULL ON UPDATE CASCADE;
