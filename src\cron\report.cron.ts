import cron from 'node-cron';
import fs from 'fs';
import { generateDailyReport } from '../utils/report.utils';
import { logger } from '../utils/logger.utils';
import path from 'path';

/**
 * Configura uma cronjob para gerar relatórios diários de distribuição de leads
 * Executa todos os dias às 20:30 (horário de São Paulo)
 */
export const setupReportCron = (): void => {
  // Agendamento: Minuto 30, Hora 20, todos os dias (*)
  // Configuração para rodar às 20:30 (horário de São Paulo)
  // node-cron usa o horário local do servidor para agendamento
  cron.schedule('30 20 * * *', async () => {
    logger.info('🕒 Iniciando geração do relatório diário de leads...');
    
    try {
      const reportPath = generateDailyReport();
      
      if (reportPath && fs.existsSync(reportPath)) {
        const reportContent = fs.readFileSync(reportPath, 'utf8');
        
        // Log indicando que o relatório foi gerado e está pronto para ser enviado
        logger.info('✅ Relatório diário gerado com sucesso!', {
          path: reportPath,
          tamanho: reportContent.length,
          data: new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })
        });

        // Exibir no console que o relatório está pronto
        console.log('\n===== RELATÓRIO DIÁRIO DE LEADS GERADO =====');
        console.log(`Arquivo: ${path.basename(reportPath)}`);
        console.log(`Local: ${reportPath}`);
        console.log('Pronto para ser enviado ao Discord');
        console.log('=========================================\n');
      } else {
        logger.error('❌ Falha ao gerar relatório diário', { path: reportPath });
      }
    } catch (error) {
      logger.error('❌ Erro durante a geração do relatório diário', error);
    }
  }, {
    scheduled: true,
    timezone: 'America/Sao_Paulo' // Garantir que o agendamento use o fuso horário correto
  });
  
  logger.info('🔄 Cronjob de relatório diário configurada para execução às 20:30 (horário de São Paulo)');
}; 